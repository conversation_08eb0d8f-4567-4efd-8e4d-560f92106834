import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cal/features/subscriptions/services/in_app_purchase_service.dart';
import 'package:equatable/equatable.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:injectable/injectable.dart';

part 'subscription_event.dart';
part 'subscription_state.dart';

// Enhanced BLoC


/// Subscription BLoC for managing in-app purchase operations
/// Refactored to use status-based state management with copyWith pattern
/// for better state consistency and easier testing
@injectable
class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  final SubscriptionService _subscriptionService;
  late StreamSubscription<List<PurchaseDetails>> _purchaseStreamSubscription;
  String? _currentPurchasingProductId;

  SubscriptionBloc(this._subscriptionService) : super(const SubscriptionState()) {
    on<LoadProducts>(_onLoadProducts);
    on<PurchaseProduct>(_onPurchaseProduct);
    on<RestorePurchases>(_onRestorePurchases);
    on<ResetState>(_onResetState);
    on<_PurchaseStreamUpdate>(_onPurchaseStreamUpdate);

    _initializePurchaseStream();
  }

  void _initializePurchaseStream() {
    _purchaseStreamSubscription = InAppPurchase.instance.purchaseStream.listen(
      (List<PurchaseDetails> purchases) {
        add(_PurchaseStreamUpdate(purchases));
      },
      onError: (error) {
        add(const _PurchaseStreamUpdate([]));
      },
    );
  }

  Future<void> _onLoadProducts(
    LoadProducts event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionStatus.loading, clearErrorMessage: true));

    try {
      if (!_subscriptionService.isAvailable) {
        emit(state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'In-app purchases not available on this device',
        ));
        return;
      }

      final products = await _subscriptionService.loadProducts();

      if (products.isEmpty) {
        emit(state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'No subscription plans available',
        ));
        return;
      }

      emit(state.copyWith(
        status: SubscriptionStatus.productsLoaded,
        products: products,
        clearErrorMessage: true,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SubscriptionStatus.error,
        errorMessage: 'Failed to load subscription plans: ${e.toString()}',
      ));
    }
  }

  Future<void> _onPurchaseProduct(
    PurchaseProduct event,
    Emitter<SubscriptionState> emit,
  ) async {
    // Prevent multiple simultaneous purchases
    if (_currentPurchasingProductId != null) {
      emit(state.copyWith(
        status: SubscriptionStatus.error,
        errorMessage: 'Purchase already in progress',
        productId: event.productId,
      ));
      return;
    }

    _currentPurchasingProductId = event.productId;
    emit(state.copyWith(
      status: SubscriptionStatus.purchaseInProgress,
      productId: event.productId,
      clearErrorMessage: true,
    ));

    try {
      final success = await _subscriptionService.purchaseSubscription(event.productId);

      if (!success) {
        _currentPurchasingProductId = null;
        emit(state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Failed to initiate purchase',
          productId: event.productId,
        ));
      }
      // If success is true, we wait for the purchase stream to handle the actual completion
    } catch (e) {
      _currentPurchasingProductId = null;
      emit(state.copyWith(
        status: SubscriptionStatus.error,
        errorMessage: 'Purchase error: ${e.toString()}',
        productId: event.productId,
      ));
    }
  }

  Future<void> _onRestorePurchases(
    RestorePurchases event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionStatus.loading, clearErrorMessage: true));

    try {
      final success = await _subscriptionService.restorePurchases();

      if (!success) {
        emit(state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Failed to restore purchases',
        ));
      }
      // Wait for purchase stream to handle restored purchases
    } catch (e) {
      emit(state.copyWith(
        status: SubscriptionStatus.error,
        errorMessage: 'Restore error: ${e.toString()}',
      ));
    }
  }

  Future<void> _onPurchaseStreamUpdate(
    _PurchaseStreamUpdate event,
    Emitter<SubscriptionState> emit,
  ) async {
    for (final purchase in event.purchases) {
      await _handlePurchaseUpdate(purchase, emit);
    }
  }

  Future<void> _handlePurchaseUpdate(
    PurchaseDetails purchase,
    Emitter<SubscriptionState> emit,
  ) async {
    switch (purchase.status) {
      case PurchaseStatus.pending:
        // Keep current state as PurchaseInProgress if this is our current purchase
        if (purchase.productID == _currentPurchasingProductId) {
          emit(state.copyWith(
            status: SubscriptionStatus.purchaseInProgress,
            productId: purchase.productID,
          ));
        }
        break;

      case PurchaseStatus.purchased:
        await _handleSuccessfulPurchase(purchase, emit);
        break;

      case PurchaseStatus.restored:
        _handleRestoredPurchase(purchase, emit);
        break;

      case PurchaseStatus.error:
        _handlePurchaseError(purchase, emit);
        break;

      case PurchaseStatus.canceled:
        _handlePurchaseCanceled(purchase, emit);
        break;
    }

    // Complete the purchase transaction
    if (purchase.pendingCompletePurchase) {
      await InAppPurchase.instance.completePurchase(purchase);
    }
  }

  Future<void> _handleSuccessfulPurchase(
    PurchaseDetails purchase,
    Emitter<SubscriptionState> emit,
  ) async {
    // Only emit validating state if this is our current purchase
    if (purchase.productID == _currentPurchasingProductId) {
      emit(state.copyWith(
        status: SubscriptionStatus.purchaseValidating,
        productId: purchase.productID,
      ));
    }

    try {
      // Validate with backend
      final isValid = await _subscriptionService.validatePurchaseWithBackend(
        purchase.verificationData.serverVerificationData,
        purchase.productID,
      );

      if (isValid) {
        final transactionId = purchase.purchaseID ?? 'unknown';
        emit(state.copyWith(
          status: SubscriptionStatus.purchaseSuccess,
          productId: purchase.productID,
          transactionId: transactionId,
          clearErrorMessage: true,
        ));
      } else {
        emit(state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Purchase validation failed. Please contact support.',
          productId: purchase.productID,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: SubscriptionStatus.error,
        errorMessage: 'Failed to validate purchase: ${e.toString()}',
        productId: purchase.productID,
      ));
    } finally {
      if (purchase.productID == _currentPurchasingProductId) {
        _currentPurchasingProductId = null;
      }
    }
  }

  void _handleRestoredPurchase(
    PurchaseDetails purchase,
    Emitter<SubscriptionState> emit,
  ) {
    // Apple already verified the purchase - no backend validation needed
    // Just collect all restored product IDs
    List<String> restoredIds = List.from(state.restoredProductIds);

    if (!restoredIds.contains(purchase.productID)) {
      restoredIds.add(purchase.productID);
    }

    emit(state.copyWith(
      status: SubscriptionStatus.restoreSuccess,
      restoredProductIds: restoredIds,
      clearErrorMessage: true,
    ));
  }

  void _handlePurchaseError(
    PurchaseDetails purchase,
    Emitter<SubscriptionState> emit,
  ) {
    final errorMessage = purchase.error?.message ?? 'Unknown purchase error';

    if (purchase.productID == _currentPurchasingProductId) {
      _currentPurchasingProductId = null;
    }

    emit(state.copyWith(
      status: SubscriptionStatus.error,
      errorMessage: errorMessage,
      productId: purchase.productID,
    ));
  }

  void _handlePurchaseCanceled(
    PurchaseDetails purchase,
    Emitter<SubscriptionState> emit,
  ) {
    if (purchase.productID == _currentPurchasingProductId) {
      _currentPurchasingProductId = null;
      // Return to products loaded state if we have products
      if (_subscriptionService.products.isNotEmpty) {
        emit(state.copyWith(
          status: SubscriptionStatus.productsLoaded,
          products: _subscriptionService.products,
          clearErrorMessage: true,
          clearProductId: true,
        ));
      } else {
        emit(state.copyWith(
          status: SubscriptionStatus.initial,
          clearErrorMessage: true,
          clearProductId: true,
        ));
      }
    }
  }

  void _onResetState(
    ResetState event,
    Emitter<SubscriptionState> emit,
  ) {
    _currentPurchasingProductId = null;
    emit(const SubscriptionState());
  }

  @override
  Future<void> close() {
    _purchaseStreamSubscription.cancel();
    return super.close();
  }
}
