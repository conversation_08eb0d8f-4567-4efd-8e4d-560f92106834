import 'dart:math';

import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/widgets/cals_container.dart';
import 'package:cal/features/home/<USER>/widgets/ingredient_card.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/pages/database_list_screen.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../common/widgets/app_image.dart';
import '../../../../core/di/injection.dart';
import '../../../../generated/assets.dart';
import '../bloc/recent_activity_bloc.dart';
import '../pages/edit_value_screen.dart';

class ItemDetailsBottomSheetBody extends StatefulWidget {
  const ItemDetailsBottomSheetBody({
    super.key,
    required this.controller,
    required this.foodModel,
    this.targetCalories = 2000,
    this.targetProtein = 150,
    this.targetCarbs = 250,
    this.targetFat = 65,
  });

  final ScrollController controller;
  final FoodModel foodModel;
  final double targetCalories;
  final double targetProtein;
  final double targetCarbs;
  final double targetFat;

  @override
  State<ItemDetailsBottomSheetBody> createState() => _ItemDetailsBottomSheetBodyState();
}

class _ItemDetailsBottomSheetBodyState extends State<ItemDetailsBottomSheetBody> with TickerProviderStateMixin {
  bool _isDeleteMode = false;
  late final AnimationController _shakeController;
  final List<String> images = [
    Assets.imagesProtien,
    Assets.imagesCarbs,
    Assets.imagesFats,
  ];

  bool isSaved = false;

  // Serving size controllers and state
  late TextEditingController _servingSizeController;
  late FocusNode _servingSizeFocusNode;

  // Store original nutrition values for calculation (per 1 serving)
  late int _originalCalories;
  late double _originalProtein;
  late double _originalCarbs;
  late double _originalFat;

  @override
  void initState() {
    super.initState();

    // Initialize serving size from the food model, default to 1 if not set
    final currentServing = widget.foodModel.serving ?? 1.0;
    _servingSizeController = TextEditingController(text: _formatServingSize(currentServing));
    _servingSizeFocusNode = FocusNode();

    // Calculate original nutrition values (per 1 serving) from current values
    _calculateOriginalNutritionValues(widget.foodModel, currentServing);

    _servingSizeController.addListener(_onServingSizeChanged);
    _servingSizeFocusNode.addListener(_onFocusChanged);

    _shakeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  void _calculateOriginalNutritionValues(FoodModel foodModel, double currentServing) {
    if (currentServing != 0) {
      _originalCalories = ((foodModel.calories ?? 0) / currentServing).round();
      _originalProtein = (foodModel.protein ?? 0.0) / currentServing;
      _originalCarbs = (foodModel.carbs ?? 0.0) / currentServing;
      _originalFat = (foodModel.fat ?? 0.0) / currentServing;
    } else {
      _originalCalories = foodModel.calories ?? 0;
      _originalProtein = foodModel.protein ?? 0.0;
      _originalCarbs = foodModel.carbs ?? 0.0;
      _originalFat = foodModel.fat ?? 0.0;
    }
  }

  @override
  void dispose() {
    _servingSizeController.dispose();
    _servingSizeFocusNode.dispose();
    _shakeController.dispose();

    super.dispose();
  }

  void _onServingSizeChanged() {
    final text = _servingSizeController.text;
    if (text.isEmpty) {
      // Handle empty input - reset to 1
      _resetToDefaultServing();
      return;
    }

    final newServingSize = double.tryParse(text);
    if (newServingSize == null || newServingSize <= 0) {
      // Handle invalid input - reset to 1
      _resetToDefaultServing();
      return;
    }

    final currentServing = widget.foodModel.serving ?? 1.0;
    if (newServingSize != currentServing) {
      _updateNutritionValues(newServingSize);
    }
  }

  void _onFocusChanged() {
    if (!_servingSizeFocusNode.hasFocus) {
      // When focus is lost, validate and update the serving size
      _validateAndUpdateServing(_servingSizeController.text);
    }
  }

  void _resetToDefaultServing() {
    final currentServing = widget.foodModel.serving ?? 1.0;
    if (currentServing != 1.0) {
      _servingSizeController.text = '1';
      _updateNutritionValues(1.0);
    }
  }

  void _validateAndUpdateServing(String value) {
    if (value.isEmpty) {
      _resetToDefaultServing();
      return;
    }

    final newServingSize = double.tryParse(value);
    if (newServingSize == null || newServingSize <= 0) {
      // Invalid input, reset to current valid value
      final currentServing = widget.foodModel.serving ?? 1.0;
      _servingSizeController.text = _formatServingSize(currentServing);
      return;
    }

    final currentServing = widget.foodModel.serving ?? 1.0;
    if (newServingSize != currentServing) {
      // Update the text field with formatted value
      _servingSizeController.text = _formatServingSize(newServingSize);
      _updateNutritionValues(newServingSize);
    }
  }

  String _formatServingSize(double servingSize) {
    // Format to remove unnecessary decimal places
    if (servingSize == servingSize.roundToDouble()) {
      return servingSize.toInt().toString();
    } else {
      return servingSize.toStringAsFixed(1);
    }
  }

  void _updateNutritionValues(double servingSize) {
    // Calculate new nutrition values based on serving size using original values
    final newCalories = (_originalCalories * servingSize).round();
    final newProtein = _originalProtein * servingSize;
    final newCarbs = _originalCarbs * servingSize;
    final newFat = _originalFat * servingSize;

    // Get current food model from the bloc state to calculate differences
    final recentFoodState = context.read<RecentActivityBloc>().state;
    FoodModel? currentFoodModel;

    // Find the current food model in the state
    for (var food in recentFoodState.foodList) {
      if (food.tempId != null && food.tempId == widget.foodModel.tempId) {
        currentFoodModel = food;
        break;
      } else if (food.imagePath == widget.foodModel.imagePath) {
        currentFoodModel = food;
        break;
      } else if (food.id == widget.foodModel.id) {
        currentFoodModel = food;
        break;
      }
    }

    // Use current food model or fallback to widget food model
    currentFoodModel ??= widget.foodModel;

    // Calculate differences for database update
    final caloriesDiff = newCalories - (currentFoodModel.calories ?? 0);
    final proteinDiff = newProtein - (currentFoodModel.protein ?? 0.0);
    final carbsDiff = newCarbs - (currentFoodModel.carbs ?? 0.0);
    final fatDiff = newFat - (currentFoodModel.fat ?? 0.0);

    // Create updated food model with new serving size
    final updatedFoodModel = currentFoodModel.copyWith(
      calories: newCalories,
      protein: newProtein,
      carbs: newCarbs,
      fat: newFat,
      serving: servingSize,
    );

    // Update the bloc with differences
    context.read<RecentActivityBloc>().add(UpdateFoodWithDifferences(
          meal: updatedFoodModel,
          caloriesDiff: caloriesDiff.toDouble(),
          carbsDiff: carbsDiff,
          proteinDiff: proteinDiff,
          fatDiff: fatDiff,
        ));
  }

  void _navigateToEditValue(BuildContext context, NutritionType type, FoodModel foodModel) {
    double targetValue;

    switch (type) {
      case NutritionType.calories:
        targetValue = widget.targetCalories;
        break;
      case NutritionType.protein:
        targetValue = widget.targetProtein;
        break;
      case NutritionType.carbs:
        targetValue = widget.targetCarbs;
        break;
      case NutritionType.fat:
        targetValue = widget.targetFat;
        break;
    }
    final recentActivityBloc = context.read<RecentActivityBloc>();

    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (ctx, animation, secondaryAnimation) => BlocProvider.value(
          value: recentActivityBloc,
          child: EditValueScreen(
            foodModel: foodModel,
            nutritionType: type,
            targetValue: targetValue,
          ),
        ),
        transitionsBuilder: (ctx, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

          final offsetAnimation = animation.drive(tween);
          return SlideTransition(
            position: offsetAnimation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  void _navigateToAddIngredients(BuildContext context, FoodModel foodModel) async {
    final recentActivityBloc = context.read<RecentActivityBloc>();

    // Create a new FoodDatabaseBloc for ingredient selection
    final foodDatabaseBloc = getIt<FoodDatabaseBloc>();

    // Store the initial selected foods count to track new additions
    final initialSelectedCount = foodDatabaseBloc.state.selectedFoods.length;

    // Navigate to DatabaseListScreen for ingredient selection
    await Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (ctx, animation, secondaryAnimation) => BlocProvider.value(
          value: foodDatabaseBloc,
          child: const DatabaseListScreen(),
        ),
        transitionsBuilder: (ctx, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

          final offsetAnimation = animation.drive(tween);
          return SlideTransition(
            position: offsetAnimation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );

    // Check if new ingredients were selected
    final currentSelectedFoods = foodDatabaseBloc.state.selectedFoods;
    if (currentSelectedFoods.length > initialSelectedCount) {
      // Get only the newly selected ingredients
      final newlySelectedIngredients = currentSelectedFoods.sublist(initialSelectedCount);
      _addIngredientsToFood(foodModel, newlySelectedIngredients, recentActivityBloc);

      // Clear the selected foods from the bloc to reset for next use
      foodDatabaseBloc.state.selectedFoods.clear();
    }
  }

  void _addIngredientsToFood(FoodModel foodModel, List<DatabaseFoodModel> selectedIngredients, RecentActivityBloc recentActivityBloc) {
    // Convert DatabaseFoodModel ingredients to Ingredient objects
    final newIngredients = selectedIngredients
        .map((dbFood) => Ingredient(
              englishName: dbFood.enName,
              arabicName: dbFood.arName,
              calories: dbFood.calories,
              protein: dbFood.protein,
              carbs: dbFood.carbs,
              fat: dbFood.fat,
              isHalal: dbFood.halal,
            ))
        .toList();

    // Add new ingredients to existing ingredient list
    final updatedIngredientList = [...foodModel.ingredientList, ...newIngredients];

    // // Debug: Print ingredient list and their isHalal values
    // print('After ADD: Ingredients:');
    // for (var ing in updatedIngredientList) {
    //   print('  ${ing.englishName} - isHalal: ${ing.isHalal}');
    // }

    // Calculate additional nutrition from new ingredients
    final additionalCalories = selectedIngredients.fold<int>(0, (sum, ingredient) => sum + (ingredient.calories ?? 0));
    final additionalProtein = selectedIngredients.fold<double>(0.0, (sum, ingredient) => sum + (ingredient.protein ?? 0.0));
    final additionalCarbs = selectedIngredients.fold<double>(0.0, (sum, ingredient) => sum + (ingredient.carbs ?? 0.0));
    final additionalFat = selectedIngredients.fold<double>(0.0, (sum, ingredient) => sum + (ingredient.fat ?? 0.0));

    // Create updated food model with new ingredients and nutrition
    final updatedFoodModel = foodModel.copyWith(
      ingredientList: updatedIngredientList,
      calories: (foodModel.calories ?? 0) + additionalCalories,
      protein: (foodModel.protein ?? 0.0) + additionalProtein,
      carbs: (foodModel.carbs ?? 0.0) + additionalCarbs,
      fat: (foodModel.fat ?? 0.0) + additionalFat,
      updateHalalFromIngredients: true,
    );

    // // Debug: Print updated FoodModel isHalal
    // print('After ADD: updatedFoodModel.isHalal = ${updatedFoodModel.isHalal}');

    // Update the bloc with the new food model and nutrition differences
    recentActivityBloc.add(UpdateFoodWithDifferences(
      meal: updatedFoodModel,
      caloriesDiff: additionalCalories.toDouble(),
      carbsDiff: additionalCarbs,
      proteinDiff: additionalProtein,
      fatDiff: additionalFat,
    ));
  }

  void _removeIngredientFromFood(FoodModel foodModel, int ingredientIndex, RecentActivityBloc recentActivityBloc) {
    final updatedIngredientList = List<Ingredient>.from(foodModel.ingredientList)..removeAt(ingredientIndex);

    // // Debug: Print ingredient list and their isHalal values
    // print('After REMOVE: Ingredients:');
    // for (var ing in updatedIngredientList) {
    //   print('  ${ing.englishName} - isHalal: ${ing.isHalal}');
    // }

    // Calculate nutritional differences for the removed ingredient
    final removedIngredient = foodModel.ingredientList[ingredientIndex];
    final removedCalories = removedIngredient.calories ?? 0;
    final removedProtein = removedIngredient.protein ?? 0.0;
    final removedCarbs = removedIngredient.carbs ?? 0.0;
    final removedFat = removedIngredient.fat ?? 0.0;

    // Calculate new total nutrition values
    final newCalories = (foodModel.calories ?? 0) - removedCalories;
    final newProtein = (foodModel.protein ?? 0.0) - removedProtein;
    final newCarbs = (foodModel.carbs ?? 0.0) - removedCarbs;
    final newFat = (foodModel.fat ?? 0.0) - removedFat;

    final updatedFoodModel = foodModel.copyWith(
      ingredientList: updatedIngredientList,
      calories: newCalories,
      protein: newProtein,
      carbs: newCarbs,
      fat: newFat,
      updateHalalFromIngredients: true,
    );

    // // Debug: Print updated FoodModel isHalal
    // print("After REMOVE: updatedFoodModel.isHalal = ${updatedFoodModel.isHalal}");

    recentActivityBloc.add(UpdateFoodWithDifferences(
      meal: updatedFoodModel,
      caloriesDiff: -removedCalories.toDouble(),
      carbsDiff: -removedCarbs,
      proteinDiff: -removedProtein,
      fatDiff: -removedFat,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_isDeleteMode) {
          setState(() {
            _isDeleteMode = false;
            _shakeController.stop();
          });
        }
      },
      behavior: HitTestBehavior.opaque,
      child: BlocProvider(
        create: (context) => getIt<FoodDatabaseBloc>(),
        child: BlocBuilder<RecentActivityBloc, RecentActivityState>(
          builder: (context, recentFoodState) {
            // Find the updated food model from the state
            FoodModel currentFoodModel = widget.foodModel;

            // Look for the updated food model in the state
            for (var food in recentFoodState.foodList) {
              if (food.tempId != null && food.tempId == widget.foodModel.tempId) {
                currentFoodModel = food;
                break;
              } else if (food.imagePath == widget.foodModel.imagePath) {
                currentFoodModel = food;
                break;
              } else if (food.id == widget.foodModel.id) {
                currentFoodModel = food;
                break;
              }
            }

            // // Debug: Print currentFoodModel.isHalal in UI build
            // print('UI BlocBuilder: currentFoodModel.isHalal = ${currentFoodModel.isHalal}');

            // Update original nutrition values based on the current food model
            _calculateOriginalNutritionValues(currentFoodModel, currentFoodModel.serving ?? 1.0);

            // Update serving size text field if the current model has a different serving size
            final currentServing = widget.foodModel.serving ?? 1.0;
            final displayedServing = double.tryParse(_servingSizeController.text) ?? 1.0;
            if (currentServing != displayedServing && !_servingSizeFocusNode.hasFocus) {
              // Only update if the text field is not focused (to avoid interfering with user input)
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _servingSizeController.text = _formatServingSize(currentServing);
              });
            }

            return ListView(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 30),
              controller: widget.controller,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (currentFoodModel.date != null)
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: context.onPrimaryColor,
                        ),
                        padding: const EdgeInsetsDirectional.all(5),
                        child: Text(
                          DateFormat('h:mm a').format(currentFoodModel.date!),
                          style: context.textTheme.labelLarge!.copyWith(
                            color: context.onSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    // HALAL CONTAINER
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: currentFoodModel.isHalal != false ? const Color(0xff27AE60) : Colors.red,
                      ),
                      padding: const EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 5),
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder: (Widget child, Animation<double> animation) {
                          return FadeTransition(
                            opacity: animation,
                            child: ScaleTransition(scale: animation, child: child),
                          );
                        },
                        child: Text(
                          currentFoodModel.isHalal != false ? LocaleKeys.common_halal.tr() : LocaleKeys.common_haram.tr(),
                          key: ValueKey<bool>(currentFoodModel.isHalal ?? true),
                          style: context.textTheme.labelLarge!.copyWith(
                            color: context.onPrimaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 15),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        (context.locale == const Locale('ar') ? currentFoodModel.arabicName : currentFoodModel.englishName) ??
                            currentFoodModel.dish ??
                            'Unknown',
                        style: context.textTheme.titleLarge!.copyWith(
                          color: context.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // Padding(
                    //   padding: const EdgeInsetsDirectional.only(end: 20),
                    //   child: BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                    //     builder: (context, state) {
                    //       return IconButton(
                    //         onPressed: () {
                    //           setState(() {
                    //             isSaved = !isSaved;
                    //           });
                    //           context.read<FoodDatabaseBloc>().add(
                    //                 AddFoodEvent(
                    //                   meal: DatabaseFoodModel(
                    //                     calories: currentFoodModel.calories,
                    //                     fat: currentFoodModel.fat,
                    //                     carbs: currentFoodModel.carbs,
                    //                     protein: currentFoodModel.protein,
                    //                     dish: currentFoodModel.dish,
                    //                     ingredients: currentFoodModel.ingredients,
                    //                     isFavoriteMeal: isSaved,
                    //                     isHalal: currentFoodModel.isHalal,
                    //                     date: DateTime.now(),
                    //                   ),
                    //                 ),
                    //               );
                    //         },
                    //         icon: Icon(
                    //           isSaved ? Icons.bookmark_outlined : Icons.bookmark_border_outlined,
                    //           color: context.primaryColor,
                    //         ),
                    //       );
                    //     },
                    //   ),
                    // ),
                  ],
                ),
                const SizedBox(height: 18),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      LocaleKeys.common_servings.tr(),
                      style: context.textTheme.titleSmall!.copyWith(
                        color: context.onSecondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        _servingSizeFocusNode.requestFocus();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: context.onPrimaryColor,
                            border: Border.all(color: context.onSecondary)),
                        padding: const EdgeInsetsDirectional.symmetric(horizontal: 30, vertical: 10),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              width: 30,
                              child: TextField(
                                controller: _servingSizeController,
                                focusNode: _servingSizeFocusNode,
                                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                textAlign: TextAlign.center,
                                style: context.textTheme.bodyMedium!.copyWith(
                                  color: context.onSecondary,
                                  fontWeight: FontWeight.bold,
                                ),
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.zero,
                                  isDense: true,
                                ),
                                onSubmitted: (value) {
                                  _servingSizeFocusNode.unfocus();
                                  _validateAndUpdateServing(value);
                                },
                                onEditingComplete: () {
                                  _validateAndUpdateServing(_servingSizeController.text);
                                },
                              ),
                            ),
                            const SizedBox(width: 10),
                            Icon(
                              Icons.mode_edit_outline,
                              color: context.onSecondary,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                AppGestureDetector(
                    onTap: () {
                      _navigateToEditValue(context, NutritionType.calories, currentFoodModel);
                    },
                    child: CalsContainer(value: currentFoodModel.calories)),

                const SizedBox(height: 10),

                // Clickable Macronutrients Row
                Row(
                  spacing: 6,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: AppGestureDetector(
                        onTap: () {
                          _navigateToEditValue(context, NutritionType.protein, currentFoodModel);
                        },
                        child: IngredientCard(
                            image: Assets.imagesProtien, item: LocaleKeys.common_protien.tr(), value: currentFoodModel.protein),
                      ),
                    ),
                    Expanded(
                      child: AppGestureDetector(
                        onTap: () {
                          _navigateToEditValue(context, NutritionType.fat, currentFoodModel);
                        },
                        child: IngredientCard(image: Assets.imagesFats, item: LocaleKeys.common_fat.tr(), value: currentFoodModel.fat),
                      ),
                    ),
                    Expanded(
                      child: AppGestureDetector(
                        onTap: () {
                          _navigateToEditValue(context, NutritionType.carbs, currentFoodModel);
                        },
                        child: IngredientCard(image: Assets.imagesCarbs, item: LocaleKeys.common_carbs.tr(), value: currentFoodModel.carbs),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 10),
                if (widget.foodModel.healthScore != null && widget.foodModel.healthScore != 0)
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: context.onSecondary.withAlpha(29),
                        width: 1,
                      ),
                      color: context.onPrimaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsetsDirectional.symmetric(horizontal: 10, vertical: 15),
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: context.background,
                          ),
                          padding: const EdgeInsetsDirectional.all(10),
                          child: const AppImage.asset(
                            Assets.imagesBrokenHeart,
                            width: 25,
                            height: 25,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    LocaleKeys.common_health_score.tr(),
                                    style: context.textTheme.labelLarge!.copyWith(
                                      color: context.onSecondary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  Text(
                                    "10 / ${widget.foodModel.healthScore!.toString()}",
                                    style: context.textTheme.bodySmall!
                                        .copyWith(color: context.onSecondary, fontWeight: FontWeight.bold, fontSize: 18),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 10),
                              LinearProgressIndicator(
                                color: context.primaryColor,
                                value: widget.foodModel.healthScore! / 10,
                                backgroundColor: context.onSecondary.withAlpha(25),
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                const SizedBox(height: 14),
                Text(
                  LocaleKeys.home_ingredients.tr(),
                  style: context.textTheme.titleSmall!.copyWith(
                    color: context.onSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

                Center(
                  child: Wrap(
                    children: [
                      ...currentFoodModel.ingredientList
                          .asMap()
                          .entries
                          .map(
                            (entry) => AppGestureDetector(
                              onLongPress: () {
                                setState(() {
                                  _isDeleteMode = true;
                                  _shakeController.repeat(reverse: true);
                                });
                              },
                              onTap: () {
                                setState(() {
                                  _isDeleteMode = false;
                                });
                              },
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Container(
                                    width: context.screenWidth * 0.2,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: context.onSecondary.withAlpha(29),
                                        width: 1,
                                      ),
                                      color: context.onPrimaryColor,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    height: 80,
                                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                    margin: const EdgeInsets.only(right: 8.0, bottom: 10),
                                    child: Center(
                                      child: Text(
                                        (context.locale == const Locale('ar') ? entry.value.arabicName : entry.value.englishName) ??
                                            "unknown",
                                        style: context.textTheme.labelLarge!.copyWith(
                                          color: context.onSecondary,
                                          fontWeight: FontWeight.w900,
                                        ),
                                        textAlign: TextAlign.center,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ),
                                  if (_isDeleteMode)
                                    Positioned(
                                      top: -4,
                                      right: -4,
                                      child: AnimatedBuilder(
                                        animation: _shakeController,
                                        builder: (context, child) {
                                          return Transform.rotate(
                                            angle: 0.05 * sin(_shakeController.value * 2 * pi),
                                            child: child,
                                          );
                                        },
                                        child: AppGestureDetector(
                                          onTap: () {
                                            final bloc = context.read<RecentActivityBloc>();
                                            _removeIngredientFromFood(currentFoodModel, entry.key, bloc);
                                          },
                                          child: Container(
                                            decoration: const BoxDecoration(
                                              color: Colors.red,
                                              shape: BoxShape.circle,
                                            ),
                                            padding: const EdgeInsets.all(2),
                                            child: const Icon(
                                              Icons.close,
                                              size: 16,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          )
                          .toList(),
                      // Add Ingredients button container here, same styling
                      AppGestureDetector(
                        onTap: () => _navigateToAddIngredients(context, currentFoodModel),
                        child: Container(
                          width: context.screenWidth * 0.2,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: context.onSecondary.withAlpha(29),
                              width: 1,
                            ),
                            color: context.onPrimaryColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          height: 80,
                          padding: const EdgeInsetsDirectional.symmetric(horizontal: 12, vertical: 10),
                          margin: const EdgeInsets.only(right: 8.0, bottom: 10),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add,
                                color: context.primaryColor,
                                size: 22,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                LocaleKeys.home_add_ingredients.tr(),
                                style: context.textTheme.labelLarge!.copyWith(
                                  color: context.primaryColor,
                                  fontWeight: FontWeight.w800,
                                  fontSize: 13,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            );
          },
        ),
      ),
    );
  }
}
