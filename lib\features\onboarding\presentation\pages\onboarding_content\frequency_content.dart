import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_option.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FrequencyContent extends StatefulWidget {
  const FrequencyContent({super.key});

  @override
  State<FrequencyContent> createState() => _FrequencyContentState();
}

class _FrequencyContentState extends State<FrequencyContent> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.exerciseFrequency != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_exercise_frequency.tr(),
          description: LocaleKeys.onboarding_we_will_use_this_information_to_personalize_your_plan.tr(),
          contentWidgets: [
            ...ExerciseFrequency.values
                .map(
                  (frequency) => OnboardingOption(
                    isSelected: state.exerciseFrequency == frequency,
                    text: frequency.localizedNameKey.tr(),
                    imagePath: frequency.icon,
                    secondaryText: frequency.value,
                    onSelected: () => context.read<OnboardingBloc>().add(UpdateExerciseFrequency(frequency)),
                  ),
                )
                .toList(),
          ],
        );
      },
    );
  }
}
