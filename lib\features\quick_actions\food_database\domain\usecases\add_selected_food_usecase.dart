import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:injectable/injectable.dart';

/// Parameters for adding selected food
class AddSelectedFoodParams {
  final List<DatabaseFoodModel> currentSelectedFoods;
  final DatabaseFoodModel foodToAdd;

  const AddSelectedFoodParams({
    required this.currentSelectedFoods,
    required this.foodToAdd,
  });
}

/// Use case for adding food to the selected foods list
@injectable
class AddSelectedFoodUseCase {
  /// Adds a food item to the selected foods list
  /// Returns the updated list of selected foods
  List<DatabaseFoodModel> call(AddSelectedFoodParams params) {
    // Check if food is already selected to avoid duplicates
    final isAlreadySelected = params.currentSelectedFoods
        .any((food) => food.id == params.foodToAdd.id);
    
    if (isAlreadySelected) {
      return params.currentSelectedFoods;
    }
    
    // Create a new list with the added food
    return List<DatabaseFoodModel>.from(params.currentSelectedFoods)
      ..add(params.foodToAdd);
  }
}

