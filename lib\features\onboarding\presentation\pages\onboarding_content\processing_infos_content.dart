import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProcessingInfosContent extends StatefulWidget {
  const ProcessingInfosContent({super.key});

  @override
  State<ProcessingInfosContent> createState() => _ProcessingResultsContentState();
}

class _ProcessingResultsContentState extends State<ProcessingInfosContent> {
  final List<String> _titles = [
    LocaleKeys.onboarding_calories.tr(),
    LocaleKeys.onboarding_carbs.tr(),
    LocaleKeys.onboarding_protien.tr(),
    LocaleKeys.onboarding_fat.tr(),
    LocaleKeys.onboarding_health_result.tr(),
  ];

  List<bool> _checkedStates = [];

  @override
  void initState() {
    super.initState();

    _checkedStates = List.filled(_titles.length, false);
    _startSequentialChecks();
    context.read<OnboardingBloc>().add(const SubmitOnboarding());
  }

  Future<void> _startSequentialChecks() async {
    for (int i = 0; i < _titles.length; i++) {
      await Future.delayed(const Duration(milliseconds: 1400));
      if (mounted) {
        setState(() {
          _checkedStates[i] = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          showDynamicHeight: false ,
          contentWidgets: [
            TweenAnimationBuilder(
              duration: const Duration(seconds: 7),
              tween: IntTween(end: 100, begin: 0),
              builder: (context, value, _) => Text(
                "$value%",
                style: context.textTheme.displayLarge!.copyWith(fontWeight: FontWeight.w700, fontSize: 54, color: context.primaryColor),
                textAlign: TextAlign.center,
              ),
              onEnd: () {
                context.read<OnboardingBloc>().add(const ProcessingComplete());
              },
            ),
            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 61.0),
              child: Text(
                LocaleKeys.onboarding_set_up_everything_for_you.tr(),
                style: context.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w800, color: context.onSecondary),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 27),
            TweenAnimationBuilder<double>(
              duration: const Duration(seconds: 7),
              tween: Tween<double>(begin: 0.0, end: 1.0),
              builder: (context, value, _) => LinearProgressIndicator(
                borderRadius: BorderRadius.circular(30),
                value: value,
                backgroundColor: Colors.grey[300],
                minHeight: 15,
                color: context.primaryColor,
              ),
            ),
            const SizedBox(height: 27),
            Text(
              LocaleKeys.onboarding_finishing_results.tr(),
              style: context.textTheme.titleSmall!.copyWith(fontWeight: FontWeight.w400, color: Colors.grey.shade700),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 27),
            Container(
              padding: const EdgeInsets.fromLTRB(28, 24, 24, 40),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: context.onSecondary,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.onboarding_daily_recommendations_for.tr(),
                    style: context.textTheme.titleMedium!.copyWith(fontWeight: FontWeight.w400, color: context.onPrimaryColor),
                  ),
                  const SizedBox(height: 7),
                  ..._titles.asMap().entries.map((entry) {
                    final index = entry.key;
                    final title = entry.value;
                    return _buildSettingsTile(title: title, isChecked: _checkedStates[index]);
                  }).toList(),
                ],
              ),
            )
          ],
        );
      },
    );
  }

  Widget _buildSettingsTile({required String title, required bool isChecked}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                width: 5,
                height: 5,
                decoration: BoxDecoration(
                  color: context.onPrimaryColor,
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              const SizedBox(width: 20),
              Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w500, color: context.onPrimaryColor),
              ),
            ],
          ),
          isChecked
              ? Icon(Icons.check_circle, color: context.onPrimaryColor, size: 20)
              : const AppImage.asset(
                  Assets.gifLoadingIcon,
                  size: 17,
                )
        ],
      ),
    );
  }
}
