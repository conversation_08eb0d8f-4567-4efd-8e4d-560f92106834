// database_food_model.dart

import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';

part 'database_food_model.g.dart';

@Collection(inheritance: false)
// ignore: must_be_immutable
class DatabaseFoodModel extends Equatable {
  Id? localId = Isar.autoIncrement; // Local DB id

  // API fields
  String? id; // API id (string)
  String? enName;
  String? arName;
  String? foodUrl;
  int? calories;
  double? carbs;
  double? fat;
  double? protein;
  bool? halal;
  String? serving;

  // Local-only fields
  @Index()
  DateTime? date;
  bool isFavoriteMeal;
  bool isMealCreated;
  bool isFromSearch;
  bool isDatabase;
  String? imagePath;
  int? remoteLogId;
  List<String> ingredients;
  @ignore
  bool isLoading;
  @ignore
  bool hasError;

  DatabaseFoodModel({
    this.localId,
    this.id,
    this.enName,
    this.arName,
    this.foodUrl,
    this.calories,
    this.carbs,
    this.fat,
    this.protein,
    this.halal,
    this.serving,
    this.date,
    this.isFavoriteMeal = false,
    this.isMealCreated = false,
    this.isFromSearch = false,
    this.isDatabase = false,
    this.imagePath,
    this.remoteLogId,
    this.ingredients = const [],
    this.isLoading = false,
    this.hasError = false,
  });

  factory DatabaseFoodModel.fromJson(Map<String, dynamic> json) {
    return DatabaseFoodModel(
      id: json['id']?.toString(),
      enName: json['en_name'] as String?,
      arName: json['ar_name'] as String?,
      foodUrl: json['food_url'] as String?,
      calories: (json['calories'] as num?)?.toInt(),
      carbs: (json['carbs'] as num?)?.toDouble(),
      fat: (json['fat'] as num?)?.toDouble(),
      protein: (json['protein'] as num?)?.toDouble(),
      halal: false,
      serving: json['serving'] as String?,
      // Local fields default
      isFromSearch: true,
      isDatabase: true,
      ingredients: json['ingredients'] is List
          ? List<String>.from(json['ingredients'].map((x) => x.toString()))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'en_name': enName,
      'ar_name': arName,
      'food_url': foodUrl,
      'calories': calories,
      'carbs': carbs,
      'fat': fat,
      'protein': protein,
      'halal': halal,
      'serving': serving,
      // Local fields
      'date': date?.toIso8601String(),
      'isFavoriteMeal': isFavoriteMeal,
      'isMealCreated': isMealCreated,
      'isFromSearch': isFromSearch,
      'isDatabase': isDatabase,
      'imagePath': imagePath,
      'remoteLogId': remoteLogId,
      'ingredients': ingredients,
      'isLoading': isLoading,
      'hasError': hasError,
    };
  }

  DatabaseFoodModel copyWith({
    String? id,
    String? enName,
    String? arName,
    String? foodUrl,
    int? calories,
    double? carbs,
    double? fat,
    double? protein,
    bool? halal,
    String? serving,
    DateTime? date,
    bool? isFavoriteMeal,
    bool? isMealCreated,
    bool? isFromSearch,
    bool? isDatabase,
    String? imagePath,
    int? remoteLogId,
    List<String>? ingredients,
    bool? isLoading,
    bool? hasError,
  }) {
    return DatabaseFoodModel(
      id: id ?? this.id,
      enName: enName ?? this.enName,
      arName: arName ?? this.arName,
      foodUrl: foodUrl ?? this.foodUrl,
      calories: calories ?? this.calories,
      carbs: carbs ?? this.carbs,
      fat: fat ?? this.fat,
      protein: protein ?? this.protein,
      halal: halal ?? this.halal,
      serving: serving ?? this.serving,
      date: date ?? this.date,
      isFavoriteMeal: isFavoriteMeal ?? this.isFavoriteMeal,
      isMealCreated: isMealCreated ?? this.isMealCreated,
      isFromSearch: isFromSearch ?? this.isFromSearch,
      isDatabase: isDatabase ?? this.isDatabase,
      imagePath: imagePath ?? this.imagePath,
      remoteLogId: remoteLogId ?? this.remoteLogId,
      ingredients: ingredients ?? this.ingredients,
      isLoading: isLoading ?? this.isLoading,
      hasError: hasError ?? this.hasError,
    );
  }

  @override
  @ignore
  List<Object?> get props => [
        id,
        enName,
        arName,
        foodUrl,
        calories,
        carbs,
        fat,
        protein,
        halal,
        serving,
        date,
        isFavoriteMeal,
        isMealCreated,
        isFromSearch,
        isDatabase,
        imagePath,
        remoteLogId,
        ingredients,
        isLoading,
        hasError,
      ];
}
