import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:injectable/injectable.dart';

/// Parameters for removing selected food
class RemoveSelectedFoodParams {
  final List<DatabaseFoodModel> currentSelectedFoods;
  final DatabaseFoodModel foodToRemove;

  const RemoveSelectedFoodParams({
    required this.currentSelectedFoods,
    required this.foodToRemove,
  });
}

/// Use case for removing food from the selected foods list
@injectable
class RemoveSelectedFoodUseCase {
  /// Removes a food item from the selected foods list
  /// Returns the updated list of selected foods
  List<DatabaseFoodModel> call(RemoveSelectedFoodParams params) {
    // Create a new list without the food to remove
    return params.currentSelectedFoods
        .where((food) => food.id != params.foodToRemove.id)
        .toList();
  }
}

