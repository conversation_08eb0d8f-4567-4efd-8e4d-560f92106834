import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/repositories/food_database_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../../core/network/exceptions.dart';

/// Parameters for deleting a meal
class DeleteMealParams {
  final DatabaseFoodModel meal;
  final List<DatabaseFoodModel> currentMyMealsList;
  final List<DatabaseFoodModel> currentRecentFoodList;
  final List<DatabaseFoodModel> currentMyFavoriteList;
  final List<DatabaseFoodModel> currentDatabaseFoodList;

  const DeleteMealParams({
    required this.meal,
    required this.currentMyMealsList,
    required this.currentRecentFoodList,
    required this.currentMyFavoriteList,
    required this.currentDatabaseFoodList,
  });
}

/// Result of delete meal operation
class DeleteMealResult {
  final List<DatabaseFoodModel> updatedMyMealsList;
  final List<DatabaseFoodModel> updatedRecentFoodList;
  final List<DatabaseFoodModel> updatedMyFavoriteList;
  final List<DatabaseFoodModel> updatedDatabaseFoodList;

  const DeleteMealResult({
    required this.updatedMyMealsList,
    required this.updatedRecentFoodList,
    required this.updatedMyFavoriteList,
    required this.updatedDatabaseFoodList,
  });
}

/// Use case for deleting a meal and updating all relevant lists
@injectable
class DeleteMealUseCase {
  final FoodDatabaseRepository _repository;

  DeleteMealUseCase({
    required FoodDatabaseRepository repository,
  }) : _repository = repository;

  /// Deletes a meal and returns updated lists
  /// Returns either a failure or the result with updated lists
  Future<Either<Failure, DeleteMealResult>> call(
    DeleteMealParams params,
  ) async {
    try {
      // Delete meal from repository
      await _repository.deleteMeal(params.meal);

      // Update all lists by removing the deleted meal
      final updatedMyMealsList = params.currentMyMealsList
          .where((meal) => meal.id != params.meal.id)
          .toList();
      
      final updatedRecentFoodList = params.currentRecentFoodList
          .where((meal) => meal.id != params.meal.id)
          .toList();
      
      final updatedMyFavoriteList = params.currentMyFavoriteList
          .where((meal) => meal.id != params.meal.id)
          .toList();
      
      final updatedDatabaseFoodList = params.currentDatabaseFoodList
          .where((meal) => meal.id != params.meal.id)
          .toList();

      return Right(DeleteMealResult(
        updatedMyMealsList: updatedMyMealsList,
        updatedRecentFoodList: updatedRecentFoodList,
        updatedMyFavoriteList: updatedMyFavoriteList,
        updatedDatabaseFoodList: updatedDatabaseFoodList,
      ));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

