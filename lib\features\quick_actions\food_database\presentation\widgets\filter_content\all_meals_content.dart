import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/bloc/recent_activity_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/food_database_card.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AllMealsContent extends StatefulWidget {
  final bool showDatabaseList;
  final String searchQuery;

  const AllMealsContent({
    super.key,
    required this.showDatabaseList,
    required this.searchQuery,
  });

  @override
  State<AllMealsContent> createState() => _AllMealsContentState();
}

class _AllMealsContentState extends State<AllMealsContent> {
  @override
  void initState() {
    super.initState();
    context.read<FoodDatabaseBloc>().add(const LoadRecentFoodEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
      builder: (context, state) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // EnterYourFoodContainer(
            //   onPressed: () {
            //     context.push(CreateMealScreen(foodDatabaseBloc: context.read<FoodDatabaseBloc>()));
            //   },
            //   text: LocaleKeys.food_database_enter_your_meals.tr(),
            //   icon: const AppImage.asset(Assets.imagesEdit),
            // ),
            // const SizedBox(height: 19),
            // Align(
            //   alignment: Alignment.centerRight,
            //   child: AppText.titleLarge(
            //     LocaleKeys.food_database_entrance_recently.tr(),
            //     color: context.onSecondary,
            //     fontWeight: FontWeight.w300,
            //   ),
            // ),
            const SizedBox(height: 40),
            state.recentFoodList.isNotEmpty
                ? ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, i) => FoodDatabaseCard(
                      title: state.recentFoodList.reversed.toList()[i].enName ?? "unknown",
                      cals: state.recentFoodList.reversed.toList()[i].calories?.toString(),
                      onAddTap: () async {
                        final meal = FoodModel(
                          fat: state.recentFoodList.reversed.toList()[i].fat,
                          carbs: state.recentFoodList.reversed.toList()[i].carbs,
                          protein: state.recentFoodList.reversed.toList()[i].protein,
                          calories: state.recentFoodList.reversed.toList()[i].calories,
                          dish: state.recentFoodList.reversed.toList()[i].enName,
                          remoteLogId: state.recentFoodList.reversed.toList()[i].remoteLogId is int
                              ? state.recentFoodList.reversed.toList()[i].remoteLogId
                              : int.tryParse(state.recentFoodList.reversed.toList()[i].id ?? ''),
                          isHalal: state.recentFoodList.reversed.toList()[i].halal,
                          date: DateTime.now(),
                          arabicName: state.recentFoodList.reversed.toList()[i].arName,
                          englishName: state.recentFoodList.reversed.toList()[i].enName,
                        );
                        getIt<RecentActivityBloc>().add(AddFood(isFromSearch: true, meal: meal));
                        context.pop();
                      },
                    ),
                    separatorBuilder: (context, index) => const SizedBox(height: 16),
                    itemCount: state.recentFoodList.length > 3 ? 3 : state.recentFoodList.length,
                  )
                : Align(
                    alignment: Alignment.center,
                    child: AppText.bodyMedium(
                      LocaleKeys.food_database_no_food.tr(),
                      color: context.onSecondary,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
            const SizedBox(height: 19),
            if (widget.showDatabaseList) ...[
              Align(
                alignment: Alignment.centerRight,
                child: AppText.titleLarge(
                  LocaleKeys.food_database_food_database.tr(),
                  color: context.onSecondary,
                  fontWeight: FontWeight.w300,
                ),
              ),
              const SizedBox(height: 19),
              BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                builder: (context, state) {
                  switch (state.searchStatus) {
                    case BlocStatus.initial:
                      return const Center(
                        child: CircularProgressIndicator.adaptive(),
                      );
                    case BlocStatus.loading:
                      return const Center(
                        child: CircularProgressIndicator.adaptive(),
                      );
                    case BlocStatus.success:
                      return state.searchedFood!.isNotEmpty
                          ? ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (context, i) => FoodDatabaseCard(
                                title: state.searchedFood?[i].enName ?? "unknown",
                                cals: state.searchedFood?[i].calories?.toString(),
                                onAddTap: () async {
                                  getIt<RecentActivityBloc>().add(
                                    AddFood(
                                      isFromSearch: true,
                                      meal: FoodModel(
                                        remoteLogId: state.searchedFood?[i].remoteLogId is int
                                            ? state.searchedFood![i].remoteLogId
                                            : int.tryParse(state.searchedFood![i].id ?? ''),
                                        fat: state.searchedFood?[i].fat,
                                        calories: state.searchedFood?[i].calories,
                                        carbs: state.searchedFood?[i].carbs,
                                        protein: state.searchedFood?[i].protein,
                                        arabicName: state.searchedFood?[i].arName,
                                        englishName: state.searchedFood?[i].enName,
                                        dish: state.searchedFood?[i].enName,
                                        isHalal: state.searchedFood?[i].halal,
                                        date: DateTime.now(),
                                      ),
                                    ),
                                  );
                                  context.pop();
                                },
                              ),
                              separatorBuilder: (context, index) => const SizedBox(height: 16),
                              itemCount: state.searchedFood!.length,
                            )
                          : Align(
                              alignment: Alignment.center,
                              child: AppText.bodyMedium(
                                LocaleKeys.food_database_no_food.tr(),
                                color: context.onSecondary,
                                fontWeight: FontWeight.w300,
                              ),
                            );
                    case BlocStatus.error:
                      return Align(
                        alignment: Alignment.center,
                        child: AppText.bodyMedium(
                          LocaleKeys.food_database_no_food.tr(),
                          color: context.onSecondary,
                          fontWeight: FontWeight.w300,
                        ),
                      );
                  }
                },
              ),
            ]
          ],
        );
      },
    );
  }
}
/*state.databaseFoodList.isNotEmpty
                  ? ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, i) => FoodDatabaseCard(
                        title: filteredDatabaseList[i].dish ?? "unknown",
                        cals: filteredDatabaseList[i].calories.toString(),
                        onAddTap: () {
                          // context.read<FoodDatabaseBloc>().add(event);
                        },
                      ),
                      separatorBuilder: (context, index) => const SizedBox(height: 16),
                      itemCount: filteredDatabaseList.length,
                    )
                  : Align(
                      alignment: Alignment.center,
                      child: AppText.bodyMedium(
                        LocaleKeys.food_database_no_food.tr(),
                        color: context.onSecondary,
                        fontWeight: FontWeight.w300,
                      ),
                    ),*/
