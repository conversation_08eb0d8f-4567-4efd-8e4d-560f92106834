import 'package:cal/common/consts/typedef.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/core/network/exceptions.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/create_meal_use_case.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/post_meal_to_log.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart';
import 'package:dartz/dartz.dart';

abstract class FoodDatabaseRepository {
  Future<void> saveMeal(DatabaseFoodModel food);
  Future<void> saveMealToLog(FoodModel food);
  Future<void> deleteMeal(DatabaseFoodModel food);
  Future<Either<Failure, DatabaseFoodModel>> saveMealToLogRemote(
      PostMealToLogParams food);
  Future<Either<Failure, DatabaseFoodModel>> createMeal(CreateMealParams food);
  Future<List<DatabaseFoodModel>> getRecentFood();
  Future<List<DatabaseFoodModel>> getMyMeals();
  Future<List<DatabaseFoodModel>> getDatabaseFood();
  Future<List<DatabaseFoodModel>> getFavoriteFood();
  DataResponse<List<DatabaseFoodModel>> searchMeals(SearchMealsParams params);
}
