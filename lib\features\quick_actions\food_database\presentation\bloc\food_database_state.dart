part of 'food_database_bloc.dart';

/// Enum representing the overall status of food database operations
enum FoodDatabaseStatus { initial, loading, success, failure }

/// Enum representing the status of specific operations like search
enum BlocStatus { initial, loading, success, error }

/// Immutable state class for FoodDatabaseBloc
class FoodDatabaseState extends Equatable {
  /// Overall status of the food database feature
  final FoodDatabaseStatus status;

  /// List of all food items
  final List<DatabaseFoodModel> foodList;

  /// List of recently accessed food items
  final List<DatabaseFoodModel> recentFoodList;

  /// List of user's custom meals
  final List<DatabaseFoodModel> myMealsList;

  /// List of user's favorite food items
  final List<DatabaseFoodModel> myFavoriteList;

  /// List of food items from the database
  final List<DatabaseFoodModel> databaseFoodList;

  /// List of currently selected food items for meal creation
  final List<DatabaseFoodModel> selectedFoods;

  /// Currently selected meal
  final DatabaseFoodModel? myMeal;

  /// Error message for failed operations
  final String? errorMessage;

  /// Status code for post to log operations
  final int? postToLogStatusCode;

  /// Newly created meal
  final DatabaseFoodModel? createdMeal;

  /// List of searched food items
  final List<DatabaseFoodModel>? searchedFood;

  /// Status of search operations
  final BlocStatus searchStatus;

  /// Status of meal creation operations
  final BlocStatus createMealStatus;

  const FoodDatabaseState({
    this.status = FoodDatabaseStatus.initial,
    this.foodList = const [],
    this.recentFoodList = const [],
    this.databaseFoodList = const [],
    this.myMealsList = const [],
    this.myFavoriteList = const [],
    this.selectedFoods = const [],
    this.errorMessage,
    this.searchedFood,
    this.searchStatus = BlocStatus.initial,
    this.createMealStatus = BlocStatus.initial,
    this.myMeal,
    this.postToLogStatusCode,
    this.createdMeal,
  });

  /// Creates a copy of this state with the given fields replaced with new values
  FoodDatabaseState copyWith({
    FoodDatabaseStatus? status,
    List<DatabaseFoodModel>? foodList,
    List<DatabaseFoodModel>? recentFoodList,
    List<DatabaseFoodModel>? myMealsList,
    List<DatabaseFoodModel>? myFavoriteList,
    List<DatabaseFoodModel>? databaseFoodList,
    List<DatabaseFoodModel>? selectedFoods,
    DatabaseFoodModel? myMeal,
    String? errorMessage,
    List<DatabaseFoodModel>? searchedFood,
    BlocStatus? searchStatus,
    BlocStatus? createMealStatus,
    int? postToLogStatusCode,
    DatabaseFoodModel? createdMeal,
    bool clearErrorMessage = false,
    bool clearSearchedFood = false,
    bool clearCreatedMeal = false,
  }) {
    return FoodDatabaseState(
      status: status ?? this.status,
      foodList: foodList ?? this.foodList,
      recentFoodList: recentFoodList ?? this.recentFoodList,
      selectedFoods: selectedFoods ?? this.selectedFoods,
      myMealsList: myMealsList ?? this.myMealsList,
      myFavoriteList: myFavoriteList ?? this.myFavoriteList,
      databaseFoodList: databaseFoodList ?? this.databaseFoodList,
      errorMessage:
          clearErrorMessage ? null : (errorMessage ?? this.errorMessage),
      searchedFood:
          clearSearchedFood ? null : (searchedFood ?? this.searchedFood),
      searchStatus: searchStatus ?? this.searchStatus,
      createMealStatus: createMealStatus ?? this.createMealStatus,
      myMeal: myMeal ?? this.myMeal,
      postToLogStatusCode: postToLogStatusCode ?? this.postToLogStatusCode,
      createdMeal: clearCreatedMeal ? null : (createdMeal ?? this.createdMeal),
    );
  }

  /// Returns true if any operation is currently loading
  bool get isLoading =>
      status == FoodDatabaseStatus.loading ||
      searchStatus == BlocStatus.loading ||
      createMealStatus == BlocStatus.loading;

  /// Returns true if there's an error in any operation
  bool get hasError =>
      status == FoodDatabaseStatus.failure ||
      searchStatus == BlocStatus.error ||
      createMealStatus == BlocStatus.error;

  @override
  List<Object?> get props => [
        status,
        foodList,
        recentFoodList,
        databaseFoodList,
        errorMessage,
        myFavoriteList,
        myMealsList,
        selectedFoods,
        searchedFood,
        searchStatus,
        createMealStatus,
        myMeal,
        postToLogStatusCode,
        createdMeal,
      ];
}
