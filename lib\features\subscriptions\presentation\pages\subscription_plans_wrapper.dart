// ignore_for_file: deprecated_member_use

import 'package:cal/features/authentication/di/authentication_injection.dart';
import 'package:cal/features/subscriptions/presentation/subscription_bloc/subscription_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SubscriptionPlansWrapper extends StatefulWidget {
  const SubscriptionPlansWrapper({super.key});

  @override
  State<SubscriptionPlansWrapper> createState() => _SubscriptionPlansWrapperState();
}

class _SubscriptionPlansWrapperState extends State<SubscriptionPlansWrapper> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<SubscriptionBloc>(),
      child: const SubscriptionPlansWrapper(),
    );
  }
}
