import 'package:cal/common/consts/typedef.dart';
import 'package:cal/features/onboarding/data/models/onboarding_model.dart';
import 'package:cal/features/onboarding/domain/repositories/onboarding_repository.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class SubmitOnboardingUsecase implements UseCase<void, SubmitOnboardingParams> {
  final OnboardingRepository onboardingRepository;

  SubmitOnboardingUsecase({required this.onboardingRepository});

  @override
  DataResponse<void> call(SubmitOnboardingParams params) {
    return onboardingRepository.submitOnboarding(onboardingParams: params);
  }
}

class SubmitOnboardingParams with Params {
  final OnboardingModel onboardingModel;
  SubmitOnboardingParams({
    required this.onboardingModel,
  });

  @override
  BodyMap getBody() => {
        "mobile_id": onboardingModel.mobileId,
        "firebase_token": onboardingModel.firebaseToken,
        "height": onboardingModel.height,
        "weight": onboardingModel.weight,
        "gender": onboardingModel.gender,
        "birthdate": onboardingModel.birthdate,
        if (onboardingModel.activity != null) "activity": onboardingModel.activity,
        "tdee_goal": onboardingModel.tdeeGoal,
        "target_weight": onboardingModel.targetWeight,
        "weekly_target": onboardingModel.weeklyTarget,
        "diet": onboardingModel.diet,
        "goal": onboardingModel.goal,
        "tried_another_app": onboardingModel.triedAnotherApp,
        "hearing_about_us": onboardingModel.hearingAboutUs,
        "calories_added": onboardingModel.caloriesAdded,
        "meals": onboardingModel.meals
      };
}
