import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/core/network/error_handeler.dart';
import 'package:cal/core/network/exceptions.dart';
import 'package:cal/features/quick_actions/food_database/data/datasources/food_remote_data_source.dart';
import 'package:cal/features/quick_actions/food_database/data/datasources/local_food_database_data_source.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/repositories/food_database_repository.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/create_meal_use_case.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/post_meal_to_log.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

@Injectable(as: FoodDatabaseRepository)
class FoodDatabaseRepositoryImpl
    with HandlingException
    implements FoodDatabaseRepository {
  final LocalFoodDatabaseDataSource localDataSource;
  final FoodRemoteDataSource remoteDataSource;

  const FoodDatabaseRepositoryImpl(this.remoteDataSource,
      {required this.localDataSource});

  @override
  Future<void> saveMeal(DatabaseFoodModel meal) async {
    await localDataSource.saveMeal(meal);
  }

  @override
  Future<void> deleteMeal(DatabaseFoodModel meal) async {
    await localDataSource.deleteMeal(meal);
  }

  @override
  Future<List<DatabaseFoodModel>> getDatabaseFood() async {
    return await localDataSource.getDatabaseMeals();
  }

  @override
  Future<List<DatabaseFoodModel>> getRecentFood() async {
    return await localDataSource.getRecentMealsFromSearch();
  }

  @override
  Future<List<DatabaseFoodModel>> getMyMeals() async {
    return await localDataSource.getCreatedMeals();
  }

  @override
  Future<List<DatabaseFoodModel>> getFavoriteFood() async {
    return await localDataSource.getFavoriteMeals();
  }

  @override
  Future<void> saveMealToLog(FoodModel food) async {
    return await localDataSource.saveMealToLog(food);
  }

  @override
  Future<Either<Failure, List<DatabaseFoodModel>>> searchMeals(
      SearchMealsParams params) {
    return remoteDataSource.searchMeals(params);
  }

  @override
  Future<Either<Failure, DatabaseFoodModel>> saveMealToLogRemote(
      PostMealToLogParams food) {
    return remoteDataSource.postMealToLog(food);
  }

  @override
  Future<Either<Failure, DatabaseFoodModel>> createMeal(CreateMealParams food) {
    return remoteDataSource.createMeal(food);
  }
}
