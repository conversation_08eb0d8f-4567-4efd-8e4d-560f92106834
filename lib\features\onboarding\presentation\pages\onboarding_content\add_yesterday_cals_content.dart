import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/yesterday_cals_card.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Goal Selection Screen
class AddYesterdayCalsContent extends StatefulWidget {
  const AddYesterdayCalsContent({super.key});

  @override
  State<AddYesterdayCalsContent> createState() => _GoalSelectionContentState();
}

class _GoalSelectionContentState extends State<AddYesterdayCalsContent> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_Add_up_to_200_calories_from_yesterday_to_todays_daily_goal.tr(),
          // centerContent: true,
          contentWidgets: [
            Row(
              spacing: 28,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  children: [
                    const SizedBox(height: 140),
                    YesterdayCalsCard(addCals: true, description: LocaleKeys.onboarding_today.tr(), progress: 0.5),
                  ],
                ),
                YesterdayCalsCard(
                  description: LocaleKeys.onboarding_yesterday.tr(),
                  progress: 0.5,
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
