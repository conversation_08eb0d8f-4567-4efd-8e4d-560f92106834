import 'package:cal/common/consts/typedef.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:injectable/injectable.dart';

import '../repositories/food_database_repository.dart';

@lazySingleton
class SearchMealsUseCase
    implements UseCase<List<DatabaseFoodModel>, SearchMealsParams> {
  final FoodDatabaseRepository foodDatabaseRepository;

  SearchMealsUseCase({required this.foodDatabaseRepository});

  @override
  DataResponse<List<DatabaseFoodModel>> call(SearchMealsParams params) {
    return foodDatabaseRepository.searchMeals(params);
  }
}

class SearchMealsParams with Params {
  final String query;

  @override
  QueryParams getParams() => {"search_query": query};

  SearchMealsParams({required this.query});
}
