import 'dart:math';

import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../../common/widgets/app_image.dart';
import '../../../../common/widgets/app_text.dart';
import '../../../../core/local_models/daily_data_model/daily_data_model.dart';

class CalsSumChart extends StatelessWidget {
  const CalsSumChart({super.key, required this.data});

  final List<DailyUserDataModel> data;

  List<String> getDaysName(Locale locale) {
    if (locale.languageCode == 'en') {
      return ['Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
    }
    return ['سب', 'أح', 'اث', 'ثلا', 'أر', 'خم', 'جم'];
  }

  double getMaxCalories(List<DailyUserDataModel> data) {
    double maxCalories = 600;
    for (int i = 0; i < data.length; i++) {
      maxCalories = max(maxCalories, data[i].sum);
    }
    return maxCalories;
  }

  int roundToNearestPowerOfTen(double number) {
    if (number == 0) return 0;

    int digits = number.ceil().toString().length;
    int power = digits - 1;
    int base = pow(10, power).toInt();
    int rounded = ((number.ceil() + base - 1) ~/ base) * base;

    return rounded;
  }

  @override
  Widget build(BuildContext context) {
    List<String> ingredients = [
      LocaleKeys.progress_carbs.tr(),
      LocaleKeys.progress_fats.tr(),
      LocaleKeys.onboarding_protien.tr(),
    ];

    List<String> images = [
      Assets.imagesCarbs,
      Assets.imagesFats,
      Assets.imagesProtien,
    ];
    return Container(
      decoration: BoxDecoration(
        color: context.onPrimaryColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: context.onSecondary.withAlpha(51),
            blurRadius: 10,
            offset: const Offset(.2, 4),
          )
        ],
      ),
      padding: const EdgeInsetsDirectional.only(start: 5, end: 5, top: 10, bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 250,
            child: SfCartesianChart(
              title: ChartTitle(
                text: LocaleKeys.progress_cals_sum.tr(),
                alignment: context.locale.languageCode == 'en' ? ChartAlignment.near : ChartAlignment.far,
                textStyle: context.textTheme.titleSmall!.copyWith(color: context.onSecondary, fontWeight: FontWeight.bold),
              ),
              plotAreaBorderWidth: 0,
              primaryXAxis: CategoryAxis(
                majorGridLines: const MajorGridLines(width: 0),
                axisLine: const AxisLine(width: 0),
                labelStyle: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              primaryYAxis: NumericAxis(
                majorGridLines: MajorGridLines(
                  width: 1,
                  color: Colors.grey[300],
                ),
                axisLine: const AxisLine(width: 0),
                labelStyle: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                minimum: 0,
                maximum: roundToNearestPowerOfTen(getMaxCalories(data)).toDouble(),
                interval: roundToNearestPowerOfTen((getMaxCalories(data) / 4)).toDouble(),
              ),
              series: <CartesianSeries>[
                // First series (Orange/Red)
                StackedColumnSeries<ChartData, String>(
                  dataSource: getChartData(data, context),
                  xValueMapper: (ChartData data, _) => data.category,
                  yValueMapper: (ChartData data, _) => data.protein,
                  name: LocaleKeys.onboarding_protien.tr(),
                  color: const Color(0xFFFF6B35),
                ),
                // Second series (Blue)
                StackedColumnSeries<ChartData, String>(
                  dataSource: getChartData(data, context),
                  xValueMapper: (ChartData data, _) => data.category,
                  yValueMapper: (ChartData data, _) => data.carbs,
                  name: LocaleKeys.progress_carbs.tr(),
                  color: const Color(0xFF4A90E2),
                ),
                // Third series (Light Orange)
                StackedColumnSeries<ChartData, String>(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(5),
                    topRight: Radius.circular(5),
                  ),
                  dataSource: getChartData(data, context),
                  xValueMapper: (ChartData data, _) => data.category,
                  yValueMapper: (ChartData data, _) => data.fats,
                  name: LocaleKeys.progress_fats.tr(),
                  color: const Color(0xFFFFB366),
                ),
              ],
            ),
          ),
          Row(
            spacing: 20,
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              3,
              (i) => FittedBox(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppImage.asset(
                      images[i],
                    ),
                    const SizedBox(width: 5),
                    AppText.labelMedium(
                      ingredients[i],
                      color: context.onSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<ChartData> getChartData(List<DailyUserDataModel> data, BuildContext context) {
    return List.generate(
      getDaysName(context.locale).length,
      (i) => ChartData(
        getDaysName(context.locale)[i],
        data[i].consumedCarbs,
        data[i].consumedFat,
        data[i].consumedProtein,
      ),
    );
  }
}

class ChartData {
  ChartData(this.category, this.carbs, this.fats, this.protein,);

  final String category;
  final double carbs;
  final double fats;
  final double protein;
}
