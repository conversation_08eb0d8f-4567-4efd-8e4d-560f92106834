import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

enum CardType { cals, carbs, fat, protien }

class OnboardingMetricCard extends StatefulWidget {
  final CardType cardType;
  final double progress;
  final VoidCallback? onTap;
  final Duration animationDuration;
  final double strokedWidth;
  final String? val;
  final BuildContext context;

  const OnboardingMetricCard({
    super.key,
    this.onTap,
    this.cardType = CardType.carbs,
    this.strokedWidth = 7,
    this.progress = 0.6,
    this.animationDuration = const Duration(milliseconds: 800),
    required this.val,
    required this.context,
  });

  @override
  State<OnboardingMetricCard> createState() => _OnboardingMetricCardState();
}

class _OnboardingMetricCardState extends State<OnboardingMetricCard> {
  String buildTitle(CardType cardType) {
    switch (cardType) {
      case CardType.cals:
        return LocaleKeys.home_calories.tr();
      case CardType.carbs:
        return LocaleKeys.onboarding_carbs.tr();
      case CardType.fat:
        return LocaleKeys.onboarding_fat.tr();
      case CardType.protien:
        return LocaleKeys.onboarding_protien.tr();
    }
  }

  String buildIcon(CardType cardType) {
    switch (cardType) {
      case CardType.cals:
        return Assets.imagesCals;
      case CardType.carbs:
        return Assets.imagesCarbs;
      case CardType.fat:
        return Assets.imagesFats;
      case CardType.protien:
        return Assets.imagesProtien;
    }
  }

  Color buildColor(CardType cardType) {
    switch (cardType) {
      case CardType.cals:
        return context.primaryColor;
      case CardType.carbs:
        return const Color(0xffFFA76E);
      case CardType.fat:
        return const Color(0xff4277FF);
      case CardType.protien:
        return const Color(0xffE45B35);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppGestureDetector(
      onTap: widget.onTap,
      child: Container(
        decoration: BoxDecoration(
          color: context.colorScheme.onPrimary,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: context.onSecondary.withAlpha(51),
              offset: const Offset(-2, 4),
              blurRadius: 10,
            ),
          ],
        ),
        padding: const EdgeInsetsDirectional.symmetric(horizontal: 12, vertical: 15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: children(context),
        ),
      ),
    );
  }

  List<Widget> children(BuildContext context) => [
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(color: context.background, borderRadius: BorderRadius.circular(20)),
              child: AppImage.asset(buildIcon(widget.cardType)),
            ),
            const SizedBox(width: 5),
            Expanded(
              child: AppText.labelLarge(
                buildTitle(widget.cardType),
                color: context.onSecondary,
                fontWeight: FontWeight.bold,
                textAlign: TextAlign.start,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Stack(
          alignment: Alignment.center,
          children: [
            TweenAnimationBuilder<double>(
              duration: widget.animationDuration,
              curve: Curves.easeInOut,
              tween: Tween<double>(
                begin: 0,
                end: widget.progress,
              ),
              builder: (context, value, _) {
                return SizedBox(
                  width: 86,
                  height: 86,
                  child: CircularProgressIndicator(
                    value: value,
                    strokeWidth: 5.5,
                    backgroundColor: context.onPrimaryContainer.withAlpha(210),
                    valueColor: AlwaysStoppedAnimation<Color>(buildColor(widget.cardType)),
                  ),
                );
              },
            ),
            widget.cardType != CardType.cals
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        widget.val!,
                        style: context.textTheme.titleSmall!.copyWith(fontWeight: FontWeight.bold, color: context.onSecondary),
                        textAlign: TextAlign.start,
                      ),
                      Text(
                        " ${LocaleKeys.onboarding_gram.tr()}",
                        style: context.textTheme.titleSmall!.copyWith(fontWeight: FontWeight.bold, color: context.onSecondary),
                        textAlign: TextAlign.start,
                      ),
                    ],
                  )
                : Text(
                    widget.val!,
                    style: context.textTheme.titleSmall!.copyWith(fontWeight: FontWeight.bold, color: context.onSecondary),
                    textAlign: TextAlign.start,
                  ),
          ],
        ),
        const SizedBox(height: 5)
        // Align(
        //   alignment: Alignment.centerRight,
        //   child: AppGestureDetector(
        //     onTap: () {
        //       context.push(OnboardingManualNutritionEditScreen(
        //         nutritionValue: widget.val!,
        //         cardType: widget.cardType,
        //         context: widget.context,
        //       ));
        //     },
        //     child: const AppImage.asset(
        //       Assets.settingsPen,
        //       size: 20,
        //     ),
        //   ),
        // ),
      ];
}

