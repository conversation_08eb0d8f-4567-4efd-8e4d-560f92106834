import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:flutter/material.dart';

class IngredientCard extends StatelessWidget {
  const IngredientCard({super.key, required this.image, required this.item, this.value = 0});

  final String image;
  final String item;
  final double? value;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: context.onSecondary.withAlpha(29),
          width: 1,
        ),
        color: context.onPrimaryColor,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsetsDirectional.symmetric(horizontal: 2, vertical: 10),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircleAvatar(
                backgroundColor: context.background,
                radius: 10,
                child: AppImage.asset(image),
              ),
              const SizedBox(width: 5),
              Flexible(
                child: AppText(
                  item,
                  maxLines: 1,
                  style: context.textTheme.labelLarge!.copyWith(
                    color: context.onSecondary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 5),
          AnimatedFlipCounter(
            duration: const Duration(milliseconds: 1100),
            value: value ?? 0,
            curve: Curves.easeInOutCubic,
            textStyle: context.textTheme.titleLarge!.copyWith(
              color: context.primaryColor,
              fontWeight: FontWeight.bold,
              letterSpacing: 0,
              height: 1,
            ),
          ),
        ],
      ),
    );
  }
}
