import 'package:cal/common/extentions/colors_extension.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../../generated/locale_keys.g.dart';

class TargetProgress<PERSON>hart extends StatefulWidget {
  const TargetProgressChart({super.key, required this.chartDate});

  final List<ProgressTarget> chartDate;

  @override
  State<TargetProgressChart> createState() => _TargetProgressChartState();
}

class _TargetProgressChartState extends State<TargetProgressChart> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 225,
      decoration: BoxDecoration(
        color: context.onPrimaryColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: context.onSecondary.withAlpha(51),
            blurRadius: 10,
            offset: const Offset(.2, 4),
          )
        ],
      ),
      padding: const EdgeInsetsDirectional.only(start: 5, end: 5, top: 10, bottom: 10),
      child: Sf<PERSON><PERSON><PERSON><PERSON><PERSON>(
        title: ChartTitle(
          text: LocaleKeys.progress_target_progress.tr(),
          alignment: context.locale.languageCode == 'en' ? ChartAlignment.near : ChartAlignment.far,
          textStyle: context.textTheme.titleSmall!.copyWith(color: context.onSecondary, fontWeight: FontWeight.bold),
        ),
        primaryXAxis: CategoryAxis(
          labelPlacement: LabelPlacement.onTicks,
          axisLine: AxisLine(color: context.onSecondary.withAlpha(25)),
          majorGridLines: MajorGridLines(width: 1, color: context.onSecondary.withAlpha(25), dashArray: const [5, 4]),
          labelStyle: context.textTheme.labelSmall!.copyWith(color: context.onSecondary.withAlpha(178), fontWeight: FontWeight.bold),
        ),
        primaryYAxis: NumericAxis(
          minimum: 0,
          maximum: 400,
          interval: 100,
          axisLine: AxisLine(color: context.onSecondary.withAlpha(25)),
          majorGridLines: MajorGridLines(color: context.onSecondary.withAlpha(25), dashArray: const [5, 4]),
          labelStyle: context.textTheme.labelMedium!.copyWith(color: context.onSecondary.withAlpha(178), fontWeight: FontWeight.bold),
        ),
        series: <SplineSeries<ProgressTarget, String>>[
          SplineSeries<ProgressTarget, String>(
            dataSource: widget.chartDate,
            xValueMapper: (ProgressTarget sales, _) => sales.day,
            yValueMapper: (ProgressTarget sales, _) => sales.value,
            markerSettings: MarkerSettings(
              isVisible: true,
              color: context.onSecondary,
              borderColor: context.onPrimaryColor,
              borderWidth: 2,
            ),
            color: context.onSecondary,
            width: 2,
          ),
        ],
        tooltipBehavior: TooltipBehavior(enable: true),
      ),
    );
  }
}

class ProgressTarget {
  final String day;
  final double value;

  ProgressTarget(this.day, this.value);
}
