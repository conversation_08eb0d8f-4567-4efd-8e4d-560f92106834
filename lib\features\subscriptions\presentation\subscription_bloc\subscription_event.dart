part of 'subscription_bloc.dart';

// Events

abstract class SubscriptionEvent extends Equatable {
  const SubscriptionEvent();

  @override
  List<Object> get props => [];
}

class LoadProducts extends SubscriptionEvent {}

class PurchaseProduct extends SubscriptionEvent {
  final String productId;

  const PurchaseProduct(this.productId);

  @override
  List<Object> get props => [productId];
}

class RestorePurchases extends SubscriptionEvent {}

class ResetState extends SubscriptionEvent {}

class _PurchaseStreamUpdate extends SubscriptionEvent {
  final List<PurchaseDetails> purchases;

  const _PurchaseStreamUpdate(this.purchases);

  @override
  List<Object> get props => [purchases];
}
