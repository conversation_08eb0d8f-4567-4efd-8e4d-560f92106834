import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_option.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class WhereDidHearOfUs extends StatefulWidget {
  const WhereDidHearOfUs({super.key});

  @override
  State<WhereDidHearOfUs> createState() => _WhereDidHearOfUsState();
}

class _WhereDidHearOfUsState extends State<WhereDidHearOfUs> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.whereDidYouHearOfUs != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_where_did_you_hear_of_us.tr(),
          contentWidgets: [
            ...HeardAboutUs.values
                .map(
                  (source) => OnboardingOption(
                    isSelected: state.whereDidYouHearOfUs == source,
                    text: source.localizedName,
                    imagePath: source.icon,
                    doesColorChange: source.doesColorChange,
                    onSelected: () => context.read<OnboardingBloc>().add(UpdateWhereDidYouHeardAboutUs(source)),
                  ),
                )
                .toList(),
          ],
        );
      },
    );
  }
}
