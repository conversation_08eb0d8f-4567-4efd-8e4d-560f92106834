import 'package:cal/core/config/endpoints.dart';
import 'package:cal/core/network/api_handler.dart';
import 'package:cal/core/network/exceptions.dart';
import 'package:cal/core/network/http_client.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/create_meal_use_case.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/post_meal_to_log.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';


@lazySingleton
class FoodRemoteDataSource with ApiHandler {
  final HTTPClient dioClient;

  FoodRemoteDataSource({required this.dioClient});

  Future<Either<Failure, List<DatabaseFoodModel>>> searchMeals(
      SearchMealsParams params) async {
    return handleApiCall(
      apiCall: () => dioClient.get(FoodDatabaseEndPoint.search,
          queryParameters: params.getParams()),
      fromJson: (json) =>
          (json as List).map((e) => DatabaseFoodModel.fromJson(e)).toList(),
    );
  }

  Future<Either<Failure, DatabaseFoodModel>> postMealToLog(
      PostMealToLogParams params) async {
    return handleApiCall(
      apiCall: () => dioClient.post(
          '${params.isMeal ? FoodDatabaseEndPoint.logMeal : FoodDatabaseEndPoint.logFood}${params.foodId}'),
      fromJson: (json) => DatabaseFoodModel.fromJson(json),
    );
  }

  Future<Either<Failure, DatabaseFoodModel>> createMeal(
      CreateMealParams params) async {
    return handleApiCall(
      apiCall: () => dioClient.post(FoodDatabaseEndPoint.createMeal,
          data: params.getBody()),
      fromJson: (json) => DatabaseFoodModel.fromJson(json),
    );
  }
}
