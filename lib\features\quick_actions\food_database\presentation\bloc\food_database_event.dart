part of 'food_database_bloc.dart';

abstract class FoodDatabaseEvent extends Equatable {
  const FoodDatabaseEvent();

  @override
  List<Object?> get props => [];
}

/// Event to add food to local storage
class AddFoodEvent extends FoodDatabaseEvent {
  final DatabaseFoodModel meal;
  final bool isForCreateMeal;
  final bool isForSaveMeal;

  const AddFoodEvent({
    required this.meal,
    this.isForCreateMeal = false,
    this.isForSaveMeal = false,
  });

  @override
  List<Object> get props => [meal, isForCreateMeal, isForSaveMeal];
}

/// Event to add food to log
class AddFoodToLogEvent extends FoodDatabaseEvent {
  final FoodModel meal;
  final bool isMeal;

  const AddFoodToLogEvent({
    required this.meal,
    required this.isMeal,
  });

  @override
  List<Object> get props => [meal, isMeal];
}

/// Event to add selected food to the selection list
class AddSelectedFoodEvent extends FoodDatabaseEvent {
  final DatabaseFoodModel food;

  const AddSelectedFoodEvent(this.food);

  @override
  List<Object> get props => [food];
}

/// Event to remove selected food from the selection list
class RemoveSelectedFoodEvent extends FoodDatabaseEvent {
  final DatabaseFoodModel food;

  const RemoveSelectedFoodEvent(this.food);

  @override
  List<Object> get props => [food];
}

/// Event to clear all selected foods
class ClearSelectedFoodsEvent extends FoodDatabaseEvent {
  const ClearSelectedFoodsEvent();
}

/// Event to load recent food items
class LoadRecentFoodEvent extends FoodDatabaseEvent {
  const LoadRecentFoodEvent();
}

/// Event to load favorite food items
class LoadFavoriteFoodEvent extends FoodDatabaseEvent {
  const LoadFavoriteFoodEvent();
}

/// Event to load user's custom meals
class LoadMyMealsEvent extends FoodDatabaseEvent {
  const LoadMyMealsEvent();
}

/// Event to load database food items
class LoadDatabaseFoodEvent extends FoodDatabaseEvent {
  const LoadDatabaseFoodEvent();
}

/// Event to search for food items
class SearchFoodEvent extends FoodDatabaseEvent {
  final SearchMealsParams params;

  const SearchFoodEvent({required this.params});

  @override
  List<Object> get props => [params];
}

/// Event to clear search results
class ClearSearchResultsEvent extends FoodDatabaseEvent {
  const ClearSearchResultsEvent();
}

/// Event to add ingredient to meal being created
class AddIngredientToMealEvent extends FoodDatabaseEvent {
  final DatabaseFoodModel ingredient;

  const AddIngredientToMealEvent(this.ingredient);

  @override
  List<Object> get props => [ingredient];
}

/// Event to create a new meal
class CreateMealEvent extends FoodDatabaseEvent {
  final CreateMealParams params;

  const CreateMealEvent({required this.params});

  @override
  List<Object> get props => [params];
}

/// Event to delete a meal
class DeleteMealEvent extends FoodDatabaseEvent {
  final DatabaseFoodModel meal;

  const DeleteMealEvent({required this.meal});

  @override
  List<Object> get props => [meal];
}

/// Event to reset error state
class ResetErrorEvent extends FoodDatabaseEvent {
  const ResetErrorEvent();
}

