import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_option.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Gender Screen
class GenderContent extends StatefulWidget {
  const GenderContent({super.key});

  @override
  State<GenderContent> createState() => _GenderContentState();
}

class _GenderContentState extends State<GenderContent> {
  @override
  void initState() {
    super.initState();
    context.read<OnboardingBloc>().add(RegisterScreenValidation((state) => state.gender != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_choose_your_gender.tr(),
          description: LocaleKeys.onboarding_we_will_use_this_information_to_personalize_your_plan.tr(),
          contentWidgets: [
            ...Gender.values
                .map(
                  (gender) => OnboardingOption(
                    isSelected: state.gender == gender,
                    text: gender.localizedName,
                    isTextCentered: true,
                    onSelected: () {
                      context.read<OnboardingBloc>().add(UpdateGender(gender));
                    },
                  ),
                )
                .toList(),
          ],
        );
      },
    );
  }
}
