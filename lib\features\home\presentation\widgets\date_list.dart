import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../common/utils/date_helper.dart';
import '../../../../common/widgets/app_text.dart';
import '../../../main/presentation/bloc/main_bloc.dart';

enum DateStatus { past, future, today }

class DateCellData {
  final DateTime date;
  final String displayText;
  final String dateLabel;
  final DateStatus status;

  const DateCellData({
    required this.date,
    required this.displayText,
    required this.dateLabel,
    required this.status,
  });

  factory DateCellData.fromMap(Map<String, String> map, String languageCode) {
    return DateCellData(
      date: DateTime.parse(map['fullDate']!),
      displayText: map[languageCode]!,
      dateLabel: map['date']!,
      status: _parseStatus(map['status']!),
    );
  }

  static DateStatus _parseStatus(String status) {
    switch (status) {
      case 'past':
        return DateStatus.past;
      case 'future':
        return DateStatus.future;
      default:
        return DateStatus.today;
    }
  }
}

bool isSameDate(DateTime a, DateTime b) {
  return a.year == b.year && a.month == b.month && a.day == b.day;
}

class DateList extends StatelessWidget {
  static const double _widgetHeight = 80.0;
  static const double _circleSize = 40.0;
  static const double _progressIndicatorSize = 37.0;
  static const double _progressStrokeWidth = 3.0;
  static const int _animationDurationMs = 800;
  static const double _spacingBetweenElements = 6.0;
  static const double _borderWidth = 1.0;
  static const double _borderRadius = 100.0;

  static const Color _successBorderColor = Color(0xff7AE157);
  static const Color _progressColor = Colors.black;
  static const int _transparentAlpha = 75;
  static const int _borderAlpha = 127;
  static const int _backgroundAlpha = 25;

  static const EdgeInsets _dottedBorderPadding = EdgeInsets.all(3);
  static const EdgeInsets _containerTopPadding = EdgeInsets.only(top: 4);
  static const EdgeInsets _futureDatePadding = EdgeInsets.only(top: 4);
  static const EdgeInsetsDirectional _selectedDatePadding = EdgeInsetsDirectional.only(top: 14);
  static const EdgeInsetsDirectional _actionDateInnerPadding = EdgeInsetsDirectional.only(top: 5);
  static const EdgeInsetsDirectional _actionDatePadding = EdgeInsetsDirectional.only(top: 11);

  static const List<double> _normalDashPattern = [5, 3];
  static const List<double> _loadingDashPattern = [10, 3];

  final ValueNotifier<DateTime> selectedDateNotifier;
  final double todayCals;

  const DateList({
    super.key,
    required this.selectedDateNotifier,
    required this.todayCals,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: _widgetHeight,
      child: ValueListenableBuilder<DateTime>(
        valueListenable: selectedDateNotifier,
        builder: (context, selectedDate, _) => PageView.builder(
          physics: const BouncingScrollPhysics(),
          reverse: true,
          itemCount: DateHelper.getPastFourWeeks(context.locale).length,
          itemBuilder: (context, pageIndex) => _buildWeekRow(context, pageIndex, selectedDate),
        ),
      ),
    );
  }

  Widget _buildWeekRow(BuildContext context, int pageIndex, DateTime selectedDate) {
    final weekData = DateHelper.getPastFourWeeks(context.locale)[pageIndex];

    return Padding(
      padding: EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          for (int dayIndex = 0; dayIndex < weekData.length; dayIndex++)
            _buildDateCell(context, pageIndex, dayIndex, weekData[dayIndex], selectedDate),
        ],
      ),
    );
  }

  Widget _buildDateCell(
    BuildContext context,
    int pageIndex,
    int dayIndex,
    Map<String, String> cellDataMap,
    DateTime selectedDate,
  ) {
    final cellData = DateCellData.fromMap(cellDataMap, context.locale.languageCode);
    final isSelected = isSameDate(cellData.date, selectedDate);

    return Expanded(
      child: AppGestureDetector(
        onTap: () => _onDateTap(cellData),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildDateIndicator(context, cellData, isSelected, pageIndex, dayIndex),
            const SizedBox(height: _spacingBetweenElements),
            _buildDateLabel(cellData),
          ],
        ),
      ),
    );
  }

  void _onDateTap(DateCellData cellData) {
    if (cellData.status != DateStatus.future) {
      selectedDateNotifier.value = cellData.date;
    }
  }

  Widget _buildDateIndicator(
    BuildContext context,
    DateCellData cellData,
    bool isSelected,
    int pageIndex,
    int dayIndex,
  ) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        if (isSelected) _buildProgressIndicator(context),
        _buildDateCircle(context, cellData, isSelected, pageIndex, dayIndex),
      ],
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: _animationDurationMs),
      curve: Curves.easeInOut,
      tween: Tween<double>(begin: 0, end: todayCals),
      builder: (context, value, _) => SizedBox(
        width: _progressIndicatorSize,
        height: _progressIndicatorSize,
        child: CircularProgressIndicator(
          value: value,
          strokeWidth: _progressStrokeWidth,
          backgroundColor: context.onSecondary.withAlpha(_backgroundAlpha),
          valueColor: const AlwaysStoppedAnimation<Color>(_progressColor),
        ),
      ),
    );
  }

  Widget _buildDateCircle(
    BuildContext context,
    DateCellData cellData,
    bool isSelected,
    int pageIndex,
    int dayIndex,
  ) {
    return switch (cellData.status) {
      DateStatus.past => _buildPastDateCircle(context, cellData, isSelected, pageIndex, dayIndex),
      DateStatus.future => _buildFutureDateCircle(context, cellData),
      DateStatus.today =>
        isSelected ? _buildSelectedTodayCircle(context, cellData) : _buildDottedCircle(context, cellData, _loadingDashPattern),
    };
  }

  Widget _buildSelectedTodayCircle(BuildContext context, DateCellData cellData) {
    return Padding(
      padding: _selectedDatePadding,
      child: AppText.labelSmall(
        cellData.displayText,
        fontWeight: FontWeight.bold,
        color: _progressColor,
      ),
    );
  }

  Widget _buildPastDateCircle(
    BuildContext context,
    DateCellData cellData,
    bool isSelected,
    int pageIndex,
    int dayIndex,
  ) {
    // If the date is selected, show the selected text (no dotted border)
    if (isSelected) {
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
        width: _circleSize,
        height: _circleSize,
        child: Center(
          child: AppText.labelSmall(
            cellData.displayText,
            fontWeight: FontWeight.bold,
            color: _progressColor,
          ),
        ),
      );
    }

    return BlocBuilder<MainBloc, MainState>(
      builder: (context, state) {
        final flatIndex = pageIndex * 7 + dayIndex;
        final hasAction = state.status == BlocStatus.success &&
            state.streaks != null &&
            flatIndex < state.streaks!.length &&
            state.streaks![flatIndex].hasAction;

        return hasAction
            ? _buildActionDateCircle(context, cellData, isSelected)
            : _buildDottedCircle(context, cellData, _normalDashPattern);
      },
    );
  }

  Widget _buildActionDateCircle(
    BuildContext context,
    DateCellData cellData,
    bool isSelected,
  ) {
    final textWidget = AppText.labelSmall(
      cellData.displayText,
      fontWeight: FontWeight.bold,
      color: _progressColor,
    );

    if (isSelected) {
      return Padding(
        padding: _selectedDatePadding,
        child: textWidget,
      );
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_borderRadius),
        border: Border.all(
          color: _successBorderColor,
          width: _borderWidth,
        ),
      ),
      padding: _actionDatePadding,
      width: _circleSize,
      height: _circleSize,
      child: Padding(
        padding: _actionDateInnerPadding,
        child: textWidget,
      ),
    );
  }

  Widget _buildDottedCircle(
    BuildContext context,
    DateCellData cellData,
    List<double> dashPattern,
  ) {
    return DottedBorder(
      options: CircularDottedBorderOptions(
        dashPattern: dashPattern,
        color: _progressColor.withAlpha(_borderAlpha),
        strokeWidth: _borderWidth,
        borderPadding: _dottedBorderPadding,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
        padding: _containerTopPadding,
        width: _circleSize,
        height: _circleSize,
        child: Center(
          child: AppText.labelSmall(
            cellData.displayText,
            fontWeight: FontWeight.bold,
            color: _progressColor,
          ),
        ),
      ),
    );
  }

  Widget _buildFutureDateCircle(BuildContext context, DateCellData cellData) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      padding: _futureDatePadding,
      width: _circleSize,
      height: _circleSize,
      child: Center(
        child: AppText.labelSmall(
          cellData.displayText,
          fontWeight: FontWeight.bold,
          color: _progressColor.withAlpha(_transparentAlpha),
        ),
      ),
    );
  }

  Widget _buildDateLabel(DateCellData cellData) {
    final isFuture = cellData.status == DateStatus.future;

    return AppText.labelSmall(
      cellData.dateLabel,
      fontWeight: FontWeight.bold,
      color: isFuture ? _progressColor.withAlpha(_transparentAlpha) : _progressColor,
    );
  }
}
