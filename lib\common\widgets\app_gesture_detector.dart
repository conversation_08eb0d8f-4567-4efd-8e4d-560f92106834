import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppGestureDetector extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const AppGestureDetector({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: key,
      onTap: () {
        if (onTap != null) {
          HapticFeedback.selectionClick();

          onTap!();
        }
      },
      onLongPress: () {
        if (onLongPress != null) {
          HapticFeedback.selectionClick();

          onLongPress!();
        }
      },
      child: child,
    );
  }
}
