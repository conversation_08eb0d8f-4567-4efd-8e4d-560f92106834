import 'dart:async';
import 'package:cal/core/network/http_client.dart';
import 'package:flutter/foundation.dart';

import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:injectable/injectable.dart';

@injectable
class SubscriptionService {
  // Remove singleton pattern - DI will handle instance management
  final InAppPurchase _inAppPurchase;
  final HTTPClient _dio;

  // Your product IDs from App Store Connect
  static const Set<String> _productIds = {
    'com.yourapp.monthly_premium',
    'com.yourapp.yearly_premium',
  };

  List<ProductDetails> _products = [];
  bool _isAvailable = false;
  bool _purchasePending = false;

  // Constructor now takes dependencies as parameters
  SubscriptionService({
    required InAppPurchase inAppPurchase,
    required HTTPClient dio,
  })  : _inAppPurchase = inAppPurchase,
        _dio = dio;

  // Initialize the service
  Future<void> initialize() async {
    // Check if in-app purchase is available
    _isAvailable = await _inAppPurchase.isAvailable();
    if (!_isAvailable) return;

    // Load available products
    await loadProducts();
  }

  // Load available subscription products
  Future<List<ProductDetails>> loadProducts() async {
    if (!_isAvailable) return [];

    try {
      final response = await _inAppPurchase.queryProductDetails(_productIds);

      if (response.error != null) {
        debugPrint('Error loading products: ${response.error}');
        return [];
      }

      _products = response.productDetails;
      return _products;
    } catch (e) {
      debugPrint('Error loading products: $e');
      return [];
    }
  }

  // Purchase a subscription
  Future<bool> purchaseSubscription(String productId) async {
    if (!_isAvailable || _purchasePending) return false;

    final product = _products.firstWhere(
      (p) => p.id == productId,
      orElse: () => throw Exception('Product not found: $productId'),
    );

    _purchasePending = true;

    try {
      final purchaseParam = PurchaseParam(productDetails: product);
      return await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
    } catch (e) {
      debugPrint('Purchase error: $e');
      _purchasePending = false;
      return false;
    }
  }

  // Restore purchases
  Future<bool> restorePurchases() async {
    if (!_isAvailable) return false;

    try {
      await _inAppPurchase.restorePurchases();
      return true;
    } catch (e) {
      debugPrint('Restore error: $e');
      return false;
    }
  }

  // Validate purchase with backend
  Future<bool> validatePurchaseWithBackend(String receiptData, String productId) async {
    try {
      final response = await _dio.post(
        '/subscription/purchase',
        data: {
          'receipt_data': receiptData,
          'product_id': productId,
        },
      );

      return response.statusCode == 200 && (response.data['success'] ?? false);
    } catch (e) {
      debugPrint('Backend validation error: $e');
      return false;
    }
  }

  // Reset purchase pending state (called by BLoC when needed)
  void resetPurchasePending() {
    _purchasePending = false;
  }

  // Optional: Dispose method for cleanup
  void dispose() {
    // Clean up any resources if needed
    // Note: Don't dispose Dio here as it might be shared
  }

  // Getters
  bool get isAvailable => _isAvailable;
  bool get purchasePending => _purchasePending;
  List<ProductDetails> get products => _products;
}
