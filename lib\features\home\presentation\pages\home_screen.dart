import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/datasources/streak_local_data_source.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/core/local_models/streak_model/streak_model.dart';
import 'package:cal/features/home/<USER>/bloc/nutrition_bloc/bloc/nutrition_bloc.dart';
import 'package:cal/features/home/<USER>/bloc/recent_activity_bloc.dart';
import 'package:cal/features/home/<USER>/widgets/food_card/food_card.dart';
import 'package:cal/features/home/<USER>/widgets/exercise_card.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:cal/features/home/<USER>/widgets/home_appbar.dart';
import 'package:cal/features/home/<USER>/widgets/nutritions_summery.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/bloc/scan_food_bloc.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:isar/isar.dart';

import '../../../../common/widgets/metric_card.dart';
import '../widgets/date_list.dart';

enum ActivityType { food, exercise }

class ActivityItem {
  final ActivityType type;
  final DateTime date;
  final FoodModel? foodModel;
  final ExerciseModel? exerciseModel;

  ActivityItem({
    required this.type,
    required this.date,
    this.foodModel,
    this.exerciseModel,
  });
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ValueNotifier<DateTime> _dateValueNotifier = ValueNotifier<DateTime>(DateTime.now());

  @override
  void initState() {
    super.initState();

    _dateValueNotifier.addListener(_onDateChanged);
    // _onDateChanged();
  }

  @override
  void dispose() {
    _dateValueNotifier.removeListener(_onDateChanged);
    _dateValueNotifier.dispose();
    super.dispose();
  }

  void _onDateChanged() {
    context.read<RecentActivityBloc>().add(LoadActivity(date: _dateValueNotifier.value));
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<NutritionBloc>(
      lazy: false,
      create: (context) => getIt<NutritionBloc>()..add(InitDailyUserData(date: _dateValueNotifier.value)),
      child: BlocListener<RecentActivityBloc, RecentActivityState>(
        listener: (context, state) {
          Future.delayed(const Duration(seconds: 1), () {
            if (context.mounted) {
              context.read<NutritionBloc>().add(LoadDailyNutritionData(date: _dateValueNotifier.value));
            }
          });
        },
        child: Scaffold(
          appBar: homeAppBar(context),
          body: SingleChildScrollView(
            padding: const EdgeInsetsDirectional.only(end: 16, start: 16, top: 0, bottom: 50),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BlocBuilder<NutritionBloc, NutritionState>(
                  builder: (context, state) {
                    return DateList(
                      selectedDateNotifier: _dateValueNotifier,
                      todayCals: calculateSafeProgress(state.targetCalories, state.consumedCalories),
                    );
                  },
                ),
                const SizedBox(height: 10),
                const NutritionSummary(),
                const SizedBox(height: 30),
                AppText.titleLarge(LocaleKeys.home_recently_added.tr(), color: context.onSecondary, fontWeight: FontWeight.bold),
                const SizedBox(height: 20),
                BlocBuilder<RecentActivityBloc, RecentActivityState>(
                  builder: (context, state) {
                    if (state.foodList.isNotEmpty || state.exerciseList.isNotEmpty) {
                      final day = DateTime.now();
                      StreakLocalDataSource(getIt<Isar>())
                          .saveStreak(StreakModel(hasAction: true, streakDate: DateTime(day.year, day.month, day.day)));
                    }

                    // Create a combined list of activities sorted by date (newest first)
                    final List<ActivityItem> combinedActivities = [];

                    // Add food items
                    for (final food in state.foodList) {
                      combinedActivities.add(ActivityItem(
                        type: ActivityType.food,
                        date: food.date ?? DateTime.now(),
                        foodModel: food,
                      ));
                    }

                    // Add exercise items
                    for (final exercise in state.exerciseList) {
                      combinedActivities.add(ActivityItem(
                        type: ActivityType.exercise,
                        date: exercise.date ?? DateTime.now(),
                        exerciseModel: exercise,
                      ));
                    }

                    // Sort by date (newest first)
                    combinedActivities.sort((a, b) => b.date.compareTo(a.date));

                    if (combinedActivities.isEmpty) {
                      return const SizedBox.shrink();
                    }

                    return AnimationLimiter(
                      key: Key(combinedActivities.map((activity) => "${activity.type}_${activity.date.millisecondsSinceEpoch}").join("_")),
                      child: ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: combinedActivities.length,
                        itemBuilder: (context, index) {
                          final activity = combinedActivities[index];

                          return AnimationConfiguration.staggeredList(
                            position: index,
                            duration: const Duration(milliseconds: 375),
                            child: SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: activity.type == ActivityType.food
                                    ? FoodCard(
                                        foodModel: activity.foodModel!,
                                        onDelete: () => context.read<RecentActivityBloc>().add(DeleteFood(activity.foodModel!)),
                                        onRetry: () => context.read<ScanFoodBloc>().add(const RetryLastScanEvent()),
                                      )
                                    : ExerciseCard(
                                        exerciseModel: activity.exerciseModel!,
                                        onDelete: () => context.read<RecentActivityBloc>().add(DeleteExercise(activity.exerciseModel!)),
                                        onRetry: () => context.read<RecentActivityBloc>().add(DeleteExercise(activity.exerciseModel!)),
                                      ),
                              ),
                            ),
                          );
                        },
                        separatorBuilder: (context, index) => const SizedBox(height: 16),
                      ),
                    );
                  },
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
