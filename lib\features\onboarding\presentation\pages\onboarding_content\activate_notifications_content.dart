import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Birth Date Screen
class ActivateNotificationsContent extends StatelessWidget {
  const ActivateNotificationsContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          // centerContent: true,
          contentWidgets: [
            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 3.0),
              child: Center(
                child: Column(
                  children: [
                    const AppImage.asset(Assets.onboardingPersonTwo),
                    const SizedBox(height: 24),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: Text(
                        LocaleKeys.onboarding_Reach_your_goal_and_activate_notifications.tr(),
                        textAlign: TextAlign.center,
                        style: context.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w400, height: 1.5),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 18.0),
                      child: Text(
                        LocaleKeys.onboarding_You_can_turn_off_any_of_the_notifications_at_any_time.tr(),
                        style: context.textTheme.titleSmall!.copyWith(fontWeight: FontWeight.w100, color: Colors.grey.shade700),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
