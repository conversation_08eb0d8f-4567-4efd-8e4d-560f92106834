import 'package:cal/core/di/injection.dart';
import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';

/// Service for calculating calories burned during exercise using specific formulas
@injectable
class CalorieCalculationService {
  /// Calorie calculation multipliers for different exercise types and intensities
  /// Based on HALALCAL formulas:
  /// Walking: low (4.8 km/h) = weight * 4.5 / 60, mid (9.66 km/h) = weight * 12.3 / 60, high (22.5 km/h) = weight * 30 / 60
  /// Resistance: low = weight * 2.57 * duration / 60, mid = weight * 4.51 * duration / 60, high = weight * 7.83 * duration / 60
  static const Map<String, Map<String, double>> _exerciseMultipliers = {
    'walking': {
      'low': 4.5, // شدة منخفضة (سرعة 4.8 كيلو متر في الساعة)
      'mid': 12.3, // شدة متوسطة (سرعة 9.66 كيلومتر في الساعة)
      'high': 30.0, // شدة عالية (سرعة 22.5 كيلومتر في الساعة)
    },
    'weight_lifting': {
      'low': 2.57, // شدة منخفضة (جهد قليل، بدون تعرق)
      'mid': 4.51, // شدة متوسطة (مع تعرق، عدات عالية)
      'high': 7.83, // شدة عالية (تمرين للفشل العضلي، تنفس سريع)
    },
    'run': {
      'low': 4.5, // Use walking low as fallback for running low
      'mid': 12.3, // Use walking mid as fallback for running mid
      'high': 30.0, // Use walking high as fallback for running high
    },
    'cycling': {
      'low': 4.5, // Use walking multipliers as fallback
      'mid': 12.3,
      'high': 30.0,
    },
    'swimming': {
      'low': 4.5, // Use walking multipliers as fallback
      'mid': 12.3,
      'high': 30.0,
    },
    'yoga': {
      'low': 2.57, // Use resistance low as fallback
      'mid': 4.51, // Use resistance mid as fallback
      'high': 7.83, // Use resistance high as fallback
    },
    'general': {
      'low': 4.5, // Default to walking multipliers
      'mid': 12.3,
      'high': 30.0,
    },
  };

  /// Calculate calories burned using HALALCAL specific formulas
  ///
  /// Walking formulas: weight * multiplier / 60 (per minute)
  /// Resistance training formulas: weight * multiplier * duration / 60
  ///
  /// [exerciseType] - Type of exercise (walking, weight_lifting, etc.)
  /// [intensity] - Intensity level (low, mid, high)
  /// [durationMinutes] - Duration of exercise in minutes
  /// [userWeight] - Optional user weight in kg. If null, will fetch from user data
  ///
  /// Returns a [CalorieCalculationResult] containing calories burned and multiplier used
  Future<CalorieCalculationResult> calculateCalories({
    required String exerciseType,
    required String intensity,
    required int durationMinutes,
    double? userWeight,
  }) async {
    // Get user weight if not provided
    final weight = userWeight ?? await _getUserWeight();

    if (weight == null || weight <= 0) {
      throw Exception('User weight not available or invalid');
    }

    // Get multiplier for the exercise type and intensity
    final multiplier = _getExerciseMultiplier(exerciseType, intensity);

    // Calculate calories based on exercise type
    final calories = _calculateCaloriesForExercise(
      exerciseType: exerciseType,
      weight: weight,
      multiplier: multiplier,
      durationMinutes: durationMinutes,
    );

    return CalorieCalculationResult(
      calories: calories,
      metValue: multiplier, // Keep this for backward compatibility
      userWeight: weight,
      durationHours: durationMinutes / 60.0,
    );
  }

  /// Calculate calories for specific exercise type using appropriate formula
  int _calculateCaloriesForExercise({
    required String exerciseType,
    required double weight,
    required double multiplier,
    required int durationMinutes,
  }) {
    final normalizedType = exerciseType.toLowerCase().replaceAll(' ', '_');

    if (normalizedType == 'walking' || normalizedType == 'run') {
      // Walking/Running formula: weight * multiplier / 60 (per minute)
      return (weight * multiplier * durationMinutes / 60).round();
    } else if (normalizedType == 'weight_lifting') {
      // Resistance training formula: weight * multiplier * duration / 60
      return (weight * multiplier * durationMinutes / 60).round();
    } else {
      // For other exercises, use walking formula as default
      return (weight * multiplier * durationMinutes / 60).round();
    }
  }

  /// Get exercise multiplier for specific exercise type and intensity
  double _getExerciseMultiplier(String exerciseType, String intensity) {
    final normalizedType = exerciseType.toLowerCase().replaceAll(' ', '_');
    final normalizedIntensity = intensity.toLowerCase();

    // Try to find exact match
    if (_exerciseMultipliers.containsKey(normalizedType)) {
      return _exerciseMultipliers[normalizedType]![normalizedIntensity] ??
          _exerciseMultipliers[normalizedType]!['mid'] ??
          _exerciseMultipliers['general']![normalizedIntensity]!;
    }

    // Fall back to general values
    return _exerciseMultipliers['general']![normalizedIntensity] ?? 12.3;
  }

  /// Get user weight from local storage
  Future<double?> _getUserWeight() async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1)).subtract(const Duration(milliseconds: 1));

      final user = await getIt<Isar>().dailyUserDataModels.filter().dateBetween(startOfDay, endOfDay).findFirst();
      if (user?.weight != null) {
        return user!.weight;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get available exercise types
  static List<String> getAvailableExerciseTypes() {
    return _exerciseMultipliers.keys.where((key) => key != 'general').toList();
  }

  /// Get available intensity levels
  static List<String> getAvailableIntensityLevels() {
    return ['low', 'mid', 'high'];
  }

  /// Get exercise multiplier for display purposes (without calculation)
  static double getMultiplierForDisplay(String exerciseType, String intensity) {
    final normalizedType = exerciseType.toLowerCase().replaceAll(' ', '_');
    final normalizedIntensity = intensity.toLowerCase();

    if (_exerciseMultipliers.containsKey(normalizedType)) {
      return _exerciseMultipliers[normalizedType]![normalizedIntensity] ??
          _exerciseMultipliers[normalizedType]!['mid'] ??
          _exerciseMultipliers['general']![normalizedIntensity]!;
    }

    return _exerciseMultipliers['general']![normalizedIntensity] ?? 12.3;
  }

  /// Get MET value for display purposes (backward compatibility)
  @Deprecated('Use getMultiplierForDisplay instead')
  static double getMetValueForDisplay(String exerciseType, String intensity) {
    return getMultiplierForDisplay(exerciseType, intensity);
  }
}

/// Result of calorie calculation
class CalorieCalculationResult {
  final int calories;
  final double metValue;
  final double userWeight;
  final double durationHours;

  const CalorieCalculationResult({
    required this.calories,
    required this.metValue,
    required this.userWeight,
    required this.durationHours,
  });

  @override
  String toString() {
    return 'CalorieCalculationResult(calories: $calories, metValue: $metValue, userWeight: $userWeight, durationHours: $durationHours)';
  }
}
