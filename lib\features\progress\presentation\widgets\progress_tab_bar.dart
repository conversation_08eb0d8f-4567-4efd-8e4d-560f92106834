import 'package:cal/common/extentions/colors_extension.dart';
import 'package:flutter/material.dart';
import 'package:tab_indicator_styler/tab_indicator_styler.dart';


class ProgressTabBar extends StatefulWidget {
  const ProgressTabBar({super.key, required this.titles, this.isFill = false, required this.onTabChanged, required this.indexValue});

  final List<String> titles;
  final bool isFill;
  final int indexValue;

  final Function(int index) onTabChanged;

  @override
  State<ProgressTabBar> createState() => _ProgressTabBarState();
}

class _ProgressTabBarState extends State<ProgressTabBar> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    _tabController = TabController(length: widget.titles.length, vsync: this);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30,
      decoration: BoxDecoration(
        color: context.onSecondary.with<PERSON><PERSON><PERSON>(20),
        borderRadius: BorderRadius.circular(4),
      ),
      child: TabBar(
        isScrollable: false,
        tabAlignment: TabAlignment.fill,
        onTap: widget.onTabChanged,
        dividerHeight: 0,
        controller: _tabController,
        tabs: List.generate(
          widget.titles.length,
          (i) => Padding(
            padding: const EdgeInsetsDirectional.only(top: 5),
            child: Text(
              widget.titles[i],
              textAlign: TextAlign.start,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: context.textTheme.bodyMedium!.copyWith(fontSize: 12, fontWeight: FontWeight.bold, color: i == widget.indexValue ? context.onPrimaryColor : context.onSecondary),
            ),
          ),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicator: RectangularIndicator(
          topLeftRadius: 4,
          bottomRightRadius: 4,
          topRightRadius: 4,
          bottomLeftRadius: 4,
          verticalPadding: 2,
          horizontalPadding: 0,
          color: context.primaryColor,
        ),
      ),
    );
  }
}
