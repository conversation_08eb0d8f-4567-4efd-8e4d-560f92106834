import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/utils/formulars.dart';
import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_metric_card.dart';
import 'package:cal/features/onboarding/presentation/widgets/progress_bar_card.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class ProcessResultContent extends StatefulWidget {
  const ProcessResultContent({super.key});

  @override
  State<ProcessResultContent> createState() => _ProcessingResultsContentState();
}

class _ProcessingResultsContentState extends State<ProcessResultContent> {
  final List<CardType> cardTypes = [CardType.cals, CardType.carbs, CardType.protien, CardType.fat];

  String getGoalTitle(Goal goal) {
    switch (goal) {
      case Goal.weightLoss:
        return LocaleKeys.onboarding_Your_goal_is_to_lose_weight.tr();
      case Goal.maintenance:
        return LocaleKeys.onboarding_Your_goal_is_to_maintain.tr();
      case Goal.weightGain:
        return LocaleKeys.onboarding_Your_goal_is_to_gain_weight.tr();
    }
  }

  Widget buildMacroRow(OnboardingState state, int startIndex, BuildContext ctx) {
    return Row(
      spacing: 10,
      children: List.generate(2, (i) {
        final index = startIndex + i;
        final value = switch (cardTypes[index]) {
          CardType.cals => "${state.cals!}",
          CardType.carbs => "${state.carbs!}",
          CardType.protien => "${state.protein!}",
          CardType.fat => "${state.fat!}",
        };
        return Expanded(
          child: OnboardingMetricCard(
            context: context,
            cardType: cardTypes[index],
            strokedWidth: 5,
            progress: 0.5,
            val: value,
          ),
        );
      }),
    );
  }

  Widget _buildSourcesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            style: context.textTheme.bodyMedium!.copyWith(
              color: context.onSecondary,
            ),
            children: [
              TextSpan(
                text: context.locale == const Locale('ar') ? "ملاحظة: " : "Note: ",
                style: const TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextSpan(
                text: context.locale == const Locale('ar')
                    ? "هذا التطبيق يقدم تقديرات غذائية بناءً على معادلات قياسية. استشر طبيبًا أو مختص تغذية قبل اتخاذ أي قرارات طبية أو غذائية."
                    : "This app provides nutritional estimates based on standard formulas. Consult a doctor or nutritionist before making any medical or dietary decisions.",
              ),
            ],
          ),
        ),
        const SizedBox(height: 8 * 3),
        Text(
          context.locale == const Locale('ar') ? "المصادر" : "Resources",
          style: context.textTheme.titleMedium!.copyWith(
            fontWeight: FontWeight.w600,
            color: context.onSecondary,
          ),
        ),
        const SizedBox(height: 8 * 2),
        Text(
          context.locale == const Locale('ar')
              ? "يتم حساب السعرات الحرارية باستخدام معادلة Mifflin-St Jeor، وهي معادلة قياسية لحساب معدل الأيض الأساسي (BMR)."
              : "Calorie calculations are based on the Mifflin-St Jeor equation, a standard formula for estimating Basal Metabolic Rate (BMR).",
          style: context.textTheme.bodyMedium,
        ),
        const SizedBox(height: 8),
        _buildSourceLink(
          context.locale == const Locale('ar') ? "معادلة Mifflin-St Jeor (PubMed)" : "Mifflin-St Jeor Equation (PubMed)",
          "https://pubmed.ncbi.nlm.nih.gov/2300561/",
        ),
        const SizedBox(height: 8),
        _buildSourceLink(
          "USDA Dietary Guidelines",
          "https://www.dietaryguidelines.gov",
        ),
        const SizedBox(height: 8),
        _buildSourceLink(
          "World Health Organization (WHO)",
          "https://www.who.int/news-room/fact-sheets/detail/obesity-and-overweight",
        ),
        const SizedBox(height: 8),
        _buildSourceLink(
          "Mayo Clinic - Healthy Weight",
          "https://www.mayoclinic.org/healthy-lifestyle/weight-loss/basics/weightloss-basics/hlv-20049483",
        ),
      ],
    );
  }

  Widget _buildSourceLink(String title, String url) {
    return InkWell(
      onTap: () async {
        final Uri uri = Uri.parse(url);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Icon(
              Icons.link,
              size: 16,
              color: context.primaryColor,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: context.textTheme.bodyMedium!.copyWith(
                  color: context.primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final isWeightLoss = state.goal == Goal.weightLoss;
        final weight = state.weight;
        final targetWeight = state.targetWeight;

        final goalAmount = (weight != null && targetWeight != null) ? (isWeightLoss ? weight - targetWeight : targetWeight - weight) : 0.0;

        final bmi = Formulars.calculateBMI(
          weightKg: state.weight!.toDouble(),
          heightCm: state.height!.toDouble(),
        );

        final age = Formulars.calculateAge(
          birthDay: state.birthDay!,
          birthMonth: state.birthMonth!,
          birthYear: state.birthYear!,
        );

        final bmr = Formulars.calculateBMR(
          weightKg: state.weight!.toDouble(),
          heightCm: state.height!.toDouble(),
          age: age,
          isMale: state.gender == Gender.male,
        );

        final tdee = Formulars.calculateTDEE(
          bmr: bmr,
          exerciseFrequency: state.exerciseFrequency!,
        );

        final proteinPercent = Formulars.proteinGramsToPercent(
          proteinGrams: state.protein!.toDouble(),
          totalCalories: state.cals!,
        );

        final carbsPercent = Formulars.proteinGramsToPercent(
          proteinGrams: state.carbs!.toDouble(),
          totalCalories: state.cals!,
        );

        final fatPercent = Formulars.proteinGramsToPercent(
          proteinGrams: state.fat!.toDouble(),
          totalCalories: state.cals!,
        );

        final healthScore = Formulars.calculateHealthScore(
          bmi: bmi,
          exerciseFrequency: state.exerciseFrequency!,
          weeklyGoalKg: state.weightChangeRate ?? 1,
          proteinPercent: proteinPercent,
          carbsPercent: carbsPercent,
          fatPercent: fatPercent,
          tdee: tdee,
          targetCalories: state.cals!,
          dietType: state.diet!,
        );

        return OnboardingScreenTemplate(
          showDynamicHeight: false,
          title: state.goal != null ? getGoalTitle(state.goal!) : LocaleKeys.onboarding_Your_goal_is_to_maintain.tr(),
          contentWidgets: [
            if (state.goal != null && state.goal != Goal.maintenance && state.weight != null && state.targetWeight != null) ...[
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 51),
                decoration: BoxDecoration(
                  color: context.background,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.all(4),
                child: Text(
                  "${goalAmount.toStringAsFixed(1)} ${LocaleKeys.onboarding_kg_by.tr()} ${Formulars.calculateWeightLossEndDate(
                    totalKgToLose: goalAmount,
                    kgPerWeek: state.weightChangeRate ?? 1,
                    locale: context.locale.languageCode,
                  )}",
                  textAlign: TextAlign.center,
                  style: context.textTheme.titleSmall!.copyWith(
                    fontWeight: FontWeight.w200,
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
            ],
            const SizedBox(height: 2),
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: context.background,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.onboarding_daily_recommendations.tr(),
                    style: context.textTheme.titleMedium!.copyWith(
                      fontWeight: FontWeight.w400,
                      color: context.onSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    LocaleKeys.onboarding_you_can_edit_anytime.tr(),
                    style: context.textTheme.bodySmall!.copyWith(
                      fontWeight: FontWeight.w200,
                      color: context.onSecondary.withAlpha(70),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 14),
                  Column(
                    children: [
                      buildMacroRow(state, 0, context),
                      const SizedBox(height: 10),
                      buildMacroRow(state, 2, context),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ProgressBarCard(value: healthScore),
                ],
              ),
            ),
            const SizedBox(height: 20),
            _buildSourcesSection(),
            const SizedBox(height: 50),
          ],
        );
      },
    );
  }
}
