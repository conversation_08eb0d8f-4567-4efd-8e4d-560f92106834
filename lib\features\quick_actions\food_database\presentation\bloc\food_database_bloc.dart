import 'dart:async';
import 'dart:developer' as developer;

import 'package:bloc/bloc.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/repositories/food_database_repository.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/add_selected_food_usecase.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/create_meal_use_case.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/delete_meal_usecase.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/post_meal_to_log.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/remove_selected_food_usecase.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/save_and_create_meal_usecase.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

part 'food_database_event.dart';
part 'food_database_state.dart';

@injectable
class FoodDatabaseBloc extends Bloc<FoodDatabaseEvent, FoodDatabaseState> {
  final FoodDatabaseRepository _foodDatabaseRepository;
  final SearchMealsUseCase _searchMealsUseCase;
  final PostMealToLog _postMealToLog;
  final CreateMealUseCase _createMealUseCase;
  final AddSelectedFoodUseCase _addSelectedFoodUseCase;
  final RemoveSelectedFoodUseCase _removeSelectedFoodUseCase;
  final SaveAndCreateMealUseCase _saveAndCreateMealUseCase;
  final DeleteMealUseCase _deleteMealUseCase;

  FoodDatabaseBloc({
    required FoodDatabaseRepository foodDatabaseRepository,
    required SearchMealsUseCase searchMealsUseCase,
    required PostMealToLog postMealToLog,
    required CreateMealUseCase createMealUseCase,
    required AddSelectedFoodUseCase addSelectedFoodUseCase,
    required RemoveSelectedFoodUseCase removeSelectedFoodUseCase,
    required SaveAndCreateMealUseCase saveAndCreateMealUseCase,
    required DeleteMealUseCase deleteMealUseCase,
  })  : _foodDatabaseRepository = foodDatabaseRepository,
        _searchMealsUseCase = searchMealsUseCase,
        _postMealToLog = postMealToLog,
        _createMealUseCase = createMealUseCase,
        _addSelectedFoodUseCase = addSelectedFoodUseCase,
        _removeSelectedFoodUseCase = removeSelectedFoodUseCase,
        _saveAndCreateMealUseCase = saveAndCreateMealUseCase,
        _deleteMealUseCase = deleteMealUseCase,
        super(const FoodDatabaseState()) {
    on<AddFoodEvent>(_onAddFood);
    on<AddFoodToLogEvent>(_onAddFoodToLog);
    on<LoadRecentFoodEvent>(_onLoadRecentFood);
    on<LoadDatabaseFoodEvent>(_onLoadDatabaseFood);
    on<LoadMyMealsEvent>(_onLoadMyMeals);
    on<LoadFavoriteFoodEvent>(_onLoadFavoriteFood);
    on<AddSelectedFoodEvent>(_onAddSelectedFood);
    on<RemoveSelectedFoodEvent>(_onRemoveSelectedFood);
    on<ClearSelectedFoodsEvent>(_onClearSelectedFoods);
    on<SearchFoodEvent>(_onSearchFood);
    on<ClearSearchResultsEvent>(_onClearSearchResults);
    on<AddIngredientToMealEvent>(_onAddIngredientToMeal);
    on<CreateMealEvent>(_onCreateMeal);
    on<DeleteMealEvent>(_onDeleteMeal);
    on<ResetErrorEvent>(_onResetError);
  }

  /// Handles searching for food items
  Future<void> _onSearchFood(
    SearchFoodEvent event,
    Emitter<FoodDatabaseState> emit,
  ) async {
    emit(state.copyWith(searchStatus: BlocStatus.loading));

    final result = await _searchMealsUseCase(event.params);

    result.fold(
      (failure) {
        developer.log('Search failed: ${failure.message}', name: 'FoodDatabaseBloc');
        emit(state.copyWith(
          searchStatus: BlocStatus.error,
          errorMessage: failure.message,
        ));
      },
      (searchResults) {
        emit(state.copyWith(
          searchStatus: BlocStatus.success,
          searchedFood: searchResults,
        ));
      },
    );
  }

  /// Handles clearing search results
  void _onClearSearchResults(
    ClearSearchResultsEvent event,
    Emitter<FoodDatabaseState> emit,
  ) {
    emit(state.copyWith(
      searchStatus: BlocStatus.initial,
      clearSearchedFood: true,
    ));
  }

  /// Handles creating a meal remotely
  Future<void> _onCreateMeal(
    CreateMealEvent event,
    Emitter<FoodDatabaseState> emit,
  ) async {
    emit(state.copyWith(createMealStatus: BlocStatus.loading));

    final result = await _createMealUseCase(event.params);

    result.fold(
      (failure) {
        developer.log('Create meal failed: ${failure.message}', name: 'FoodDatabaseBloc');
        emit(state.copyWith(
          createMealStatus: BlocStatus.error,
          errorMessage: failure.message,
        ));
      },
      (createdMeal) {
        emit(state.copyWith(
          createMealStatus: BlocStatus.success,
          createdMeal: createdMeal,
        ));
      },
    );
  }

  /// Handles adding food to log
  Future<void> _onAddFoodToLog(
    AddFoodToLogEvent event,
    Emitter<FoodDatabaseState> emit,
  ) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));

    try {
      await _foodDatabaseRepository.saveMealToLog(event.meal);
      emit(state.copyWith(status: FoodDatabaseStatus.success));

      // Post meal to log remotely
      final postParams = PostMealToLogParams(
        foodId: event.isMeal ? event.meal.id : event.meal.remoteLogId!,
        isMeal: event.isMeal,
      );

      await _postMealToLog(postParams);
    } catch (e) {
      developer.log('Add food to log failed: $e', name: 'FoodDatabaseBloc');
      emit(state.copyWith(
        status: FoodDatabaseStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Handles adding ingredient to meal being created
  void _onAddIngredientToMeal(
    AddIngredientToMealEvent event,
    Emitter<FoodDatabaseState> emit,
  ) {
    final params = AddSelectedFoodParams(
      currentSelectedFoods: state.selectedFoods,
      foodToAdd: event.ingredient,
    );

    final updatedSelectedFoods = _addSelectedFoodUseCase(params);

    emit(state.copyWith(
      status: FoodDatabaseStatus.success,
      selectedFoods: updatedSelectedFoods,
    ));

    developer.log(
      'Selected foods updated: ${updatedSelectedFoods.length} items',
      name: 'FoodDatabaseBloc',
    );
  }

  /// Handles adding selected food
  void _onAddSelectedFood(
    AddSelectedFoodEvent event,
    Emitter<FoodDatabaseState> emit,
  ) {
    final params = AddSelectedFoodParams(
      currentSelectedFoods: state.selectedFoods,
      foodToAdd: event.food,
    );

    final updatedSelectedFoods = _addSelectedFoodUseCase(params);

    emit(state.copyWith(
      status: FoodDatabaseStatus.success,
      selectedFoods: updatedSelectedFoods,
    ));
  }

  /// Handles removing selected food
  void _onRemoveSelectedFood(
    RemoveSelectedFoodEvent event,
    Emitter<FoodDatabaseState> emit,
  ) {
    final params = RemoveSelectedFoodParams(
      currentSelectedFoods: state.selectedFoods,
      foodToRemove: event.food,
    );

    final updatedSelectedFoods = _removeSelectedFoodUseCase(params);

    emit(state.copyWith(
      status: FoodDatabaseStatus.success,
      selectedFoods: updatedSelectedFoods,
    ));
  }

  /// Handles clearing all selected foods
  void _onClearSelectedFoods(
    ClearSelectedFoodsEvent event,
    Emitter<FoodDatabaseState> emit,
  ) {
    emit(state.copyWith(
      status: FoodDatabaseStatus.success,
      selectedFoods: const [],
    ));
  }

  /// Handles adding food with complex save and create logic
  Future<void> _onAddFood(
    AddFoodEvent event,
    Emitter<FoodDatabaseState> emit,
  ) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));

    final params = SaveAndCreateMealParams(
      meal: event.meal,
      isForCreateMeal: event.isForCreateMeal,
      currentMyMealsList: state.myMealsList,
    );

    final result = await _saveAndCreateMealUseCase(params);

    result.fold(
      (failure) {
        developer.log('Add food failed: ${failure.message}', name: 'FoodDatabaseBloc');
        emit(state.copyWith(
          status: FoodDatabaseStatus.failure,
          errorMessage: failure.message,
        ));
      },
      (saveResult) {
        emit(state.copyWith(
          status: FoodDatabaseStatus.success,
          myMealsList: saveResult.updatedMyMealsList,
          createdMeal: saveResult.createdMeal,
        ));
      },
    );
  }

  /// Handles loading my meals
  Future<void> _onLoadMyMeals(
    LoadMyMealsEvent event,
    Emitter<FoodDatabaseState> emit,
  ) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));

    try {
      final foodList = await _foodDatabaseRepository.getMyMeals();
      emit(state.copyWith(
        status: FoodDatabaseStatus.success,
        myMealsList: foodList,
      ));
    } catch (e) {
      developer.log('Load my meals failed: $e', name: 'FoodDatabaseBloc');
      emit(state.copyWith(
        status: FoodDatabaseStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Handles loading favorite food
  Future<void> _onLoadFavoriteFood(
    LoadFavoriteFoodEvent event,
    Emitter<FoodDatabaseState> emit,
  ) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));

    try {
      final foodList = await _foodDatabaseRepository.getFavoriteFood();
      emit(state.copyWith(
        status: FoodDatabaseStatus.success,
        myFavoriteList: foodList,
      ));
    } catch (e) {
      developer.log('Load favorite food failed: $e', name: 'FoodDatabaseBloc');
      emit(state.copyWith(
        status: FoodDatabaseStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Handles loading recent food
  Future<void> _onLoadRecentFood(
    LoadRecentFoodEvent event,
    Emitter<FoodDatabaseState> emit,
  ) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));

    try {
      final foodList = await _foodDatabaseRepository.getRecentFood();
      emit(state.copyWith(
        status: FoodDatabaseStatus.success,
        recentFoodList: foodList,
      ));
    } catch (e) {
      developer.log('Load recent food failed: $e', name: 'FoodDatabaseBloc');
      emit(state.copyWith(
        status: FoodDatabaseStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Handles loading database food
  Future<void> _onLoadDatabaseFood(
    LoadDatabaseFoodEvent event,
    Emitter<FoodDatabaseState> emit,
  ) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));

    try {
      final foodList = await _foodDatabaseRepository.getDatabaseFood();
      emit(state.copyWith(
        status: FoodDatabaseStatus.success,
        databaseFoodList: foodList,
      ));
    } catch (e) {
      developer.log('Load database food failed: $e', name: 'FoodDatabaseBloc');
      emit(state.copyWith(
        status: FoodDatabaseStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Handles deleting a meal
  Future<void> _onDeleteMeal(
    DeleteMealEvent event,
    Emitter<FoodDatabaseState> emit,
  ) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));

    final params = DeleteMealParams(
      meal: event.meal,
      currentMyMealsList: state.myMealsList,
      currentRecentFoodList: state.recentFoodList,
      currentMyFavoriteList: state.myFavoriteList,
      currentDatabaseFoodList: state.databaseFoodList,
    );

    final result = await _deleteMealUseCase(params);

    result.fold(
      (failure) {
        developer.log('Delete meal failed: ${failure.message}', name: 'FoodDatabaseBloc');
        emit(state.copyWith(
          status: FoodDatabaseStatus.failure,
          errorMessage: failure.message,
        ));
      },
      (deleteResult) {
        emit(state.copyWith(
          status: FoodDatabaseStatus.success,
          myMealsList: deleteResult.updatedMyMealsList,
          recentFoodList: deleteResult.updatedRecentFoodList,
          myFavoriteList: deleteResult.updatedMyFavoriteList,
          databaseFoodList: deleteResult.updatedDatabaseFoodList,
        ));
      },
    );
  }

  /// Handles resetting error state
  void _onResetError(
    ResetErrorEvent event,
    Emitter<FoodDatabaseState> emit,
  ) {
    emit(state.copyWith(
      status: FoodDatabaseStatus.initial,
      searchStatus: BlocStatus.initial,
      createMealStatus: BlocStatus.initial,
      clearErrorMessage: true,
    ));
  }
}
