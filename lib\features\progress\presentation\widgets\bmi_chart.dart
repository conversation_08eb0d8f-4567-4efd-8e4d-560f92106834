import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class Bmi<PERSON>hart extends StatelessWidget {
  BmiChart({super.key, required this.bmi});

  final double bmi;

  List<String> get title => [
        LocaleKeys.progress_underweight.tr(),
        LocaleKeys.progress_healthy.tr(),
        LocaleKeys.progress_overweight.tr(),
        LocaleKeys.progress_obese.tr(),
      ];

  // Fixed: Colors now match the gradient order
  final List<Color> colors = [
    const Color(0xff4A90E2), // Blue - Underweight
    const Color(0xff27AE60), // Green - Healthy
    const Color(0xffF1C40F), // Yellow - Overweight
    const Color(0xffE74C3C), // Red - Obese
  ];

  String getBmiCategory(double bmi) {
    if (bmi < 18.5) return LocaleKeys.progress_underweight.tr();
    if (bmi < 25) return LocaleKeys.progress_healthy.tr();
    if (bmi < 30) return LocaleKeys.progress_overweight.tr();
    return LocaleKeys.progress_obese.tr();
  }

  // Fixed: Get the appropriate color for current BMI category
  Color getBmiCategoryColor(double bmi) {
    if (bmi < 18.5) return colors[0]; // Blue - Underweight
    if (bmi < 25) return colors[1]; // Green - Healthy
    if (bmi < 30) return colors[2]; // Yellow - Overweight
    return colors[3]; // Red - Obese
  }

  double getCustomBmiPercent(double bmi) {
    if (bmi < 0) return 0;

    if (bmi <= 18.5) {
      return (bmi / 18.5) * 25;
    } else if (bmi <= 24.9) {
      return 25 + ((bmi - 18.5) / (24.9 - 18.5)) * 25;
    } else if (bmi <= 29.9) {
      return 50 + ((bmi - 25) / (29.9 - 25)) * 25;
    } else if (bmi <= 40) {
      return 75 + ((bmi - 30) / (40 - 30)) * 25;
    } else {
      // Fixed: Cap at 100% to prevent cursor from going outside
      return 100;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = context.locale.languageCode == 'ar';

    return Container(
      padding: const EdgeInsetsDirectional.only(start: 15, end: 15, top: 20, bottom: 20),
      decoration: BoxDecoration(
        color: context.onPrimaryColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: context.onSecondary.withAlpha(51),
            blurRadius: 10,
            offset: const Offset(.2, 4),
          )
        ],
      ),
      // padding: const EdgeInsetsDirectional.only(start: 10, end: 10, top: 15, bottom: 26),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          AppText.titleLarge(
            LocaleKeys.progress_bmi.tr(),
            fontWeight: FontWeight.bold,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center, // Fixed: Better alignment
            children: [
              AppText.titleLarge(
                bmi.toStringAsFixed(2),
                fontWeight: FontWeight.bold,
                color: context.primaryColor,
              ),
              const SizedBox(width: 5),
              AppText.labelLarge(
                LocaleKeys.progress_your_current_weight_is.tr(),
                fontWeight: FontWeight.bold,
                color: context.onSecondary,
              ),
              const SizedBox(width: 10),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  // Fixed: Use dynamic color based on BMI category
                  color: getBmiCategoryColor(bmi),
                ),
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 10, vertical: 5),
                // Fixed: Center the text properly
                alignment: Alignment.center,
                child: Text(
                  getBmiCategory(bmi),
                  style: context.textTheme.labelLarge!.copyWith(
                    color: context.onPrimaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 20,
            child: LayoutBuilder(builder: (context, constraints) {
              double barWidth = constraints.maxWidth;
              double bmiPercent = (getCustomBmiPercent(bmi) / 100).clamp(0.0, 1.0);

              // Fixed: Ensure cursor stays within the bar bounds
              double pointerPosition = (bmiPercent * (barWidth - 2)).clamp(0.0, barWidth - 2);

              return Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    height: 8,
                    width: barWidth,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      gradient: LinearGradient(
                        begin: isRTL ? Alignment.centerRight : Alignment.centerLeft,
                        end: isRTL ? Alignment.centerLeft : Alignment.centerRight,
                        colors: const [
                          Color(0xff4A90E2), // Blue - Underweight
                          Color(0xff27AE60), // Green - Healthy
                          Color(0xffF1C40F), // Yellow - Overweight
                          Color(0xffE74C3C), // Red - Obese
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    // Fixed: Improved positioning logic with bounds checking
                    left: isRTL ? (barWidth - pointerPosition - 2) : pointerPosition,
                    child: Container(
                      width: 2,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(1),
                      ),
                    ),
                  ),
                ],
              );
            }),
          ),
          const SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(
              4,
              (i) => Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: colors[i],
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Flexible(
                    child: Text(
                      title[i],
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
