import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../main/presentation/bloc/main_bloc.dart';

class StreakDialog extends StatefulWidget {
  const StreakDialog({Key? key, required this.bloc}) : super(key: key);

  final MainBloc bloc;

  @override
  State<StreakDialog> createState() => _StreakDialogState();
}

class _StreakDialogState extends State<StreakDialog> {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    widget.bloc.add(GetStreaksNumberEvent());
  }

  @override
  Widget build(BuildContext context) {
    final int streakDays = widget.bloc.state.streaksNumber ?? 0;
    // String streakText;

    // if (streakDays == 0) {
    //   streakText = LocaleKeys.home_streak_days_0.tr();
    // } else if (streakDays == 1) {
    //   streakText = LocaleKeys.home_streak_days_1.tr();
    // } else if (streakDays == 2) {
    //   streakText = LocaleKeys.home_streak_days_2.tr();
    // } else if (streakDays >= 3 && streakDays <= 10) {
    //   streakText = '${LocaleKeys.home_streak_days_3_10.tr()} $streakDays';
    // } else {
    //   streakText = '${LocaleKeys.home_streak_days_10_plus.tr()} $streakDays';
    // }
    List<String> dayTitle = [
      LocaleKeys.home_weekdays_shortcuts_saturday.tr(),
      LocaleKeys.home_weekdays_shortcuts_sunday.tr(),
      LocaleKeys.home_weekdays_shortcuts_monday.tr(),
      LocaleKeys.home_weekdays_shortcuts_tuesday.tr(),
      LocaleKeys.home_weekdays_shortcuts_wednesday.tr(),
      LocaleKeys.home_weekdays_shortcuts_thursday.tr(),
      LocaleKeys.home_weekdays_shortcuts_friday.tr(),
    ];

    String streakText = LocaleKeys.streak_days.plural(
      streakDays,
      namedArgs: {'count': streakDays.toString()},
    );
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const AppImage.asset(Assets.imagesOrangeAi, height: 24, width: 24),
            const SizedBox(height: 20.0),
            const AppImage.asset(
              Assets.imagesFlam,
              height: 120,
            ),
            const SizedBox(height: 20.0),
            BlocBuilder<MainBloc, MainState>(
              bloc: widget.bloc,
              builder: (context, state) {
                return Text(
                  streakText,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: context.primaryColor,
                    fontSize: 22.0,
                    fontWeight: FontWeight.bold,
                  ),
                );
              },
            ),
            const SizedBox(height: 20.0),
            BlocBuilder<MainBloc, MainState>(
              bloc: widget.bloc,
              builder: (context, state) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(
                    dayTitle.length,
                    (i) => _buildDayCircle(
                      dayTitle[i],
                      state.streaks![i].hasAction,
                      context,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 20.0),
            // Text(
            //   'أنت في قمة نشاطك! كل يوم مهم لتحقيق هدفك',
            //   textAlign: TextAlign.center,
            //   style: TextStyle(
            //     color: context.onSecondary.withAlpha(175),
            //     fontSize: 16.0,
            //   ),
            // ),
            const SizedBox(height: 30.0),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 15.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                ),
                child: Text(
                  LocaleKeys.common_next.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDayCircle(String day, bool isChecked, BuildContext context) {
    return Column(
      children: [
        Text(
          day,
          style: TextStyle(
            color: isChecked ? context.primaryColor : context.onSecondary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 5.0),
        Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: isChecked ? context.primaryColor : context.onSecondary.withAlpha(51)),
            color: isChecked ? context.primaryColor.withAlpha(51) : context.onSecondary.withAlpha(12),
          ),
          child: isChecked ? const Icon(Icons.check, color: Colors.orange, size: 20.0) : null,
        ),
      ],
    );
  }
}
