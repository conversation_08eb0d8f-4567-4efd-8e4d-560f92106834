import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/repositories/food_database_repository.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/create_meal_use_case.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../../../core/network/exceptions.dart';

/// Parameters for saving and creating a meal
class SaveAndCreateMealParams {
  final DatabaseFoodModel meal;
  final bool isForCreateMeal;
  final List<DatabaseFoodModel> currentMyMealsList;

  const SaveAndCreateMealParams({
    required this.meal,
    required this.isForCreateMeal,
    required this.currentMyMealsList,
  });
}

/// Result of save and create meal operation
class SaveAndCreateMealResult {
  final List<DatabaseFoodModel> updatedMyMealsList;
  final DatabaseFoodModel? createdMeal;

  const SaveAndCreateMealResult({
    required this.updatedMyMealsList,
    this.createdMeal,
  });
}

/// Use case for saving a meal locally and optionally creating it remotely
@injectable
class SaveAndCreateMealUseCase {
  final FoodDatabaseRepository _repository;
  final CreateMealUseCase _createMealUseCase;

  SaveAndCreateMealUseCase({
    required FoodDatabaseRepository repository,
    required CreateMealUseCase createMealUseCase,
  })  : _repository = repository,
        _createMealUseCase = createMealUseCase;

  /// Saves a meal locally and optionally creates it remotely
  /// Returns either a failure or the result with updated meals list
  Future<Either<Failure, SaveAndCreateMealResult>> call(
    SaveAndCreateMealParams params,
  ) async {
    try {
      // Save meal locally
      await _repository.saveMeal(params.meal);

      // Update the meals list
      List<DatabaseFoodModel> updatedMyMealsList = 
          List.from(params.currentMyMealsList);
      
      if (params.isForCreateMeal && params.meal.isMealCreated) {
        updatedMyMealsList.insert(0, params.meal);
      }

      DatabaseFoodModel? createdMeal;

      // Create meal remotely if needed
      if (params.isForCreateMeal) {
        final createMealParams = CreateMealParams(
          name: params.meal.arName!,
          ingredients: params.meal.ingredients,
          fats: params.meal.fat!,
          carbs: params.meal.carbs!,
          protein: params.meal.protein!,
          cals: params.meal.calories!,
        );

        final result = await _createMealUseCase(createMealParams);
        
        return result.fold(
          (failure) => Left(failure),
          (meal) {
            createdMeal = meal;
            return Right(SaveAndCreateMealResult(
              updatedMyMealsList: updatedMyMealsList,
              createdMeal: createdMeal,
            ));
          },
        );
      }

      return Right(SaveAndCreateMealResult(
        updatedMyMealsList: updatedMyMealsList,
        createdMeal: createdMeal,
      ));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

