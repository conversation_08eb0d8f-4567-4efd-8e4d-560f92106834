import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../../../common/widgets/app_image.dart';
import '../../../../generated/assets.dart';

class CalsContainer extends StatelessWidget {
  const CalsContainer({super.key, this.value = 0});

  final int? value;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: context.onSecondary.withAlpha(29),
          width: 1,
        ),
        color: context.onPrimaryColor,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsetsDirectional.symmetric(horizontal: 10, vertical: 15),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: context.background,
            ),
            padding: const EdgeInsetsDirectional.all(10),
            child: const AppImage.asset(
              Assets.imagesCals,
              width: 25,
              height: 25,
            ),
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                LocaleKeys.home_calorie.tr(),
                style: context.textTheme.labelLarge!.copyWith(
                  color: context.onSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 5),
              AnimatedFlipCounter(
                duration: const Duration(milliseconds: 1100),
                value: value ?? 0,
                curve: Curves.easeInOutCubic,
                textStyle: context.textTheme.titleLarge!.copyWith(
                  color: context.primaryColor,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0,
                  height: 1,
                ),
              ),
              // Text(
              //   value!.toStringAsFixed(1),
              //   style: context.textTheme.titleLarge!.copyWith(
              //     color: context.primaryColor,
              //     fontWeight: FontWeight.bold,
              //   ),
              // ),
            ],
          )
        ],
      ),
    );
  }
}
