import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/birthdate_selector.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Birth Date Screen
class BirthDateContent extends StatefulWidget {
  const BirthDateContent({super.key});

  @override
  State<BirthDateContent> createState() => _BirthDateContentState();
}

class _BirthDateContentState extends State<BirthDateContent> {
  @override
  void initState() {
    super.initState();

    context.read<OnboardingBloc>().add(RegisterScreenValidation(
        (state) => state.birthDay != null && state.birthMonth != null && state.birthYear != null));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        return OnboardingScreenTemplate(
          title: LocaleKeys.onboarding_birthday.tr(),
          description: LocaleKeys.onboarding_we_will_use_this_information_to_personalize_your_plan.tr(),
          contentWidgets: [
            BirthDateSelector(
              initialDay: state.birthDay ?? DateTime.now().day,
              initialMonth: state.birthMonth ?? DateTime.now().month,
              initialYear: state.birthYear ?? DateTime.now().year,
              onDayChanged: (day) => context.read<OnboardingBloc>().add(UpdateBirthDay(day)),
              onMonthChanged: (month) => context.read<OnboardingBloc>().add(UpdateBirthMonth(month)),
              onYearChanged: (year) => context.read<OnboardingBloc>().add(UpdateBirthYear(year)),
            ),
          ],
        );
      },
    );
  }
}
