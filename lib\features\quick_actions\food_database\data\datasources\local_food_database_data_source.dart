import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';

@injectable
class LocalFoodDatabaseDataSource {
  final Isar _isar;

  LocalFoodDatabaseDataSource(this._isar);
  Future<void> saveMeal(DatabaseFoodModel food) async {
    await _isar.writeTxn(() async {
      await _isar.databaseFoodModels.put(food);
    });
  }

  Future<void> saveMealToLog(FoodModel food) async {
    await _isar.writeTxn(() async {
      await _isar.foodModels.put(food);
    });
  }

  Future<List<DatabaseFoodModel>> getAllMeals(DateTime date) async {
    return await _isar.databaseFoodModels
        .filter()
        .dateBetween(DateTime(date.year, date.month, date.day), DateTime(date.year, date.month, date.day + 1))
        .findAll();
  }

  Future<List<DatabaseFoodModel>> getRecentMealsFromSearch() async {
    return await _isar.databaseFoodModels.filter().isFromSearchEqualTo(true).findAll();
  }

  Future<List<DatabaseFoodModel>> getDatabaseMeals() async {
    return await _isar.databaseFoodModels.filter().isDatabaseEqualTo(true).findAll();
  }

  Future<List<DatabaseFoodModel>> getCreatedMeals() async {
    return await _isar.databaseFoodModels.filter().isMealCreatedEqualTo(true).findAll();
  }

  Future<List<DatabaseFoodModel>> getFavoriteMeals() async {
    return await _isar.databaseFoodModels.filter().isFavoriteMealEqualTo(true).findAll();
  }

  Future<void> deleteMeal(DatabaseFoodModel food) async {
    await _isar.writeTxn(() async {
      await _isar.databaseFoodModels.delete(food.localId!);
    });
  }
}
