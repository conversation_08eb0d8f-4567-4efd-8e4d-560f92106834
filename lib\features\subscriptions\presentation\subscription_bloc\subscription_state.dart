part of 'subscription_bloc.dart';

/// Enum representing the status of subscription operations
enum SubscriptionStatus {
  /// Initial state before any operations
  initial,

  /// Loading products or performing operations
  loading,

  /// Products have been successfully loaded
  productsLoaded,

  /// Purchase is in progress
  purchaseInProgress,

  /// Purchase is being validated with backend
  purchaseValidating,

  /// Purchase completed successfully
  purchaseSuccess,

  /// Purchases restored successfully
  restoreSuccess,

  /// An error occurred
  error,
}

/// Single state class for subscription management using status-based approach
/// This replaces the previous multiple state classes with a single state that uses
/// status enum and copyWith pattern for better state management
class SubscriptionState extends Equatable {
  /// Current status of the subscription operations
  final SubscriptionStatus status;

  /// List of available subscription products
  final List<ProductDetails> products;

  /// Error message when status is error
  final String? errorMessage;

  /// Product ID for purchase-related operations
  final String? productId;

  /// Transaction ID for successful purchases
  final String? transactionId;

  /// List of restored product IDs
  final List<String> restoredProductIds;

  const SubscriptionState({
    this.status = SubscriptionStatus.initial,
    this.products = const [],
    this.errorMessage,
    this.productId,
    this.transactionId,
    this.restoredProductIds = const [],
  });

  /// Creates a copy of this state with the given fields replaced with new values
  SubscriptionState copyWith({
    SubscriptionStatus? status,
    List<ProductDetails>? products,
    String? errorMessage,
    String? productId,
    String? transactionId,
    List<String>? restoredProductIds,
    bool clearErrorMessage = false,
    bool clearProductId = false,
    bool clearTransactionId = false,
  }) {
    return SubscriptionState(
      status: status ?? this.status,
      products: products ?? this.products,
      errorMessage: clearErrorMessage ? null : (errorMessage ?? this.errorMessage),
      productId: clearProductId ? null : (productId ?? this.productId),
      transactionId: clearTransactionId ? null : (transactionId ?? this.transactionId),
      restoredProductIds: restoredProductIds ?? this.restoredProductIds,
    );
  }

  bool get isLoading => status == SubscriptionStatus.loading;
  bool get hasError => status == SubscriptionStatus.error;
  bool get hasProducts => status == SubscriptionStatus.productsLoaded && products.isNotEmpty;
  bool get isPurchasing => status == SubscriptionStatus.purchaseInProgress;
  bool get isValidating => status == SubscriptionStatus.purchaseValidating;

  @override
  List<Object?> get props => [
        status,
        products,
        errorMessage,
        productId,
        transactionId,
        restoredProductIds,
      ];
}
