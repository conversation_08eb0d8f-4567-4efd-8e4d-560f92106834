import 'package:cal/features/quick_actions/food_database/domain/repositories/food_database_repository.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:injectable/injectable.dart';

import '../../../../../common/consts/typedef.dart';

@lazySingleton
class CreateMealUseCase
    implements UseCase<DatabaseFoodModel, CreateMealParams> {
  final FoodDatabaseRepository foodDatabaseRepository;

  CreateMealUseCase({required this.foodDatabaseRepository});

  @override
  DataResponse<DatabaseFoodModel> call(CreateMealParams params) {
    return foodDatabaseRepository.createMeal(params);
  }
}

class CreateMealParams with Params {
  final String name;
  final List<String> ingredients;
  final double fats;
  final double carbs;
  final double protein;
  final int cals;

  CreateMealParams({
    required this.name,
    required this.ingredients,
    required this.fats,
    required this.carbs,
    required this.protein,
    required this.cals,
  });

  @override
  BodyMap getBody() => {
        "name": name,
        "calories": cals,
        "fats": fats,
        "carbs": carbs,
        "protein": protein,
        "ingredients[]": ingredients,
      };
}
